<template>
  <div>
    <v-dialog v-model="showBookingForm" persistent :scrollable="false" id="bookingDialog" width="100%" max-width="1365px" content-class="booking-modal h-full overflow-hidden">
        <v-form ref="form" v-model="valid" class="h-full pt-2">
          <div class="bg-white h-full">
            <v-row class="ml-0 h-full">
              <v-col cols="9" class="booking-form">
                <v-card-title class="border-bottom mb-2 d-flex justify-space-between align-center pr-0">
                  <h4>
                    Add Booking
                  </h4>
                  <v-spacer/>
                  <v-btn
                      v-if="id == 0 && isRaceFormatEnabled"
                      class="text_capitalize bg-transparent px-0"
                      :outlined="false"
                      elevation="0"
                      min-width="40px"
                      @click="makeRaceFormat()"
                  >
                    <RacingIcon class="mr-1"/>
                    <!--                    Maintenance-->
                  </v-btn>
                  <v-btn
                      v-if="id == 0"
                      class="text_capitalize bg-transparent px-0"
                      :outlined="false"
                      elevation="0"
                      min-width="40px"
                      @click="addMaintenance()"
                  >
                    <MaintenanceIcon class="mr-1"/>
                    <!--                    Maintenance-->
                  </v-btn>
                </v-card-title>
                <v-card-text class="overflow-y-auto pr-0 h-full customer-details" :style="{'maxHeight': (this.maxHeight - 80) + 'px'}">
                  <div
                      class="bg-white bordered rounded-lg pb-8"
                  >
                    <div class="d-flex justify-space-between align-center bg-neon opacity-10 border-bottom pa-4">
                      <div class="title-text">Booking Details</div>
                      <div v-if="!id && perCapacity == 0" class="d-flex justify-end align-start">
                        <v-switch
                            id="repeat_booking"
                            v-model="bookingForm.repeat"
                            class="mr-0 pa-0 mt-0"
                            dense
                            hide-details
                            label=""
                            @change="changeRepeatBookingSwitch"
                        />
                        <label class="toggle-switch-label" for="repeat_booking">Repeat bookings</label>
                      </div>
                    </div>
                    <div class="px-4 pt-2">
                      <v-row v-if="!bookingForm.repeat || id > 0" class="mt-0" dense>
                        <v-col sm="3" md="3">
                          <label for="">Booking date</label>
                          <date-field
                              v-model="bookingForm.date"
                              :dayName="true"
                              :dense="true"
                              :disabled="id==0|| perCapacity ===0"
                              :hide-details="true"
                              :readonly="id==0|| perCapacity ===0"
                              background-color="#fff"
                              class-name="q-text-field shadow-0"
                              label=""
                              outlined
                          >
                          </date-field>
                        </v-col>
                        <v-col sm="3" md="3">
                          <label class="text-dark-gray font-medium text-xs" for="">Start Time</label>
                          <v-select
                              v-if="bookingForm.start_time"
                              v-model="bookingForm.start_time"
                              :items="startTimes"
                              :menu-props="{ bottom: true, offsetY: true }"
                              :readonly="bookingForm.repeat || fullDay || id == 0 || perCapacity ===0"
                              :rules="[(v) => !!v || 'Start time is required']"
                              background-color="#fff"
                              class="q-autocomplete shadow-0"
                              dense
                              hide-details="auto"
                              hint="Start Time"
                              item-text="formatted"
                              item-value="time"
                              outlined
                              @change="changeStartTime"
                          >
                          </v-select>
                        </v-col>
                        <v-col sm="3" md="3">
                          <label class="text-dark-gray font-medium text-xs" for="">End Time</label>
                          <v-select
                              v-model="bookingForm.end_time"
                              :items="endTimes"
                              :menu-props="{ bottom: true, offsetY: true }"
                              :readonly="bookingForm.repeat || fullDay || perCapacity == 1 || (id != 0 && perCapacity ===0)"
                              :rules="[(v) => !!v || 'End time is required']"
                              background-color="#fff"
                              class="q-autocomplete shadow-0"
                              dense
                              hide-details="auto"
                              item-text="formatted"
                              item-value="time"
                              outlined
                              @change="getRentalProducts"
                          >
                            <template v-slot:item="{ item }">
                              <span v-html="item.formatted"></span>
                            </template>
                            <template v-slot:selection="{ item }">
                              <span v-html="item.formatted"></span>
                            </template>
                          </v-select>
                        </v-col>
                        <v-col v-if="allowTrainerAssignment" :sm="allowTrainerAssignment ? 3:4" :md="allowTrainerAssignment ? 3:4">
                          <label for="" class="text-dark-gray font-medium text-xs">Trainers</label>
                          <v-select
                              outlined
                              :menu-props="{ bottom: true, offsetY: true }"
                              background-color="#fff"
                              item-text="name"
                              item-value="id"
                              :items="allowed_trainers"
                              v-model="bookingForm.assigned_trainers"
                              :rules="[(v) => !!v || 'Trainer is required']"
                              dense
                              hide-details="auto"
                              class="q-autocomplete shadow-0"
                              multiple
                          >
                          </v-select>
                        </v-col>
                        <v-col v-if="!bookingForm.repeat" sm="3" md="3">
                          <label>Facility*</label>
                          <v-text-field
                              :value="facility_name"
                              label=""
                              outlined
                              readonly
                              background-color="#fff"
                              dense
                              hide-details="auto"
                              class="q-text-field shadow-0"
                          ></v-text-field>
                        </v-col>
                      </v-row>
                    </div>
                    <v-row v-if="bookingForm.repeat && !id">
                      <v-col
                          v-for="(repeat, index) in $store.getters.getRepeats"
                          :key="`repeat_${index}`"
                          md="12"
                      >
                        <repeat-booking
                            :date="repeat.date"
                            :enableOvernightBooking="enableOvernightBooking"
                            :facilities="facilities"
                            :facilityId="facility_id"
                            :index="index"
                            :minBookingTime="minBookingTime"
                            :venueServiceId="venue_service_id"
                            @remove="removeRepeatRow"
                            @repeat="setRepeatData"
                        ></repeat-booking>
                      </v-col>
                    </v-row>
                    <div v-if="bookingForm.repeat && !id" class="ml-4">
                      <v-btn
                          class="mt-4"
                          color="teal-color"
                          dark
                          elevation="0"
                          @click="addRepeatRow"
                      >
                        + Add Booking
                      </v-btn>

                    </div>
                    <!--                <v-row class="mt-0">-->
                    <!--                  <v-col-->
                    <!--                    sm="4"-->
                    <!--                    md="4"-->
                    <!--                    v-if="-->
                    <!--                      checkReadPermission($modules.salesTeam.management.slug)-->
                    <!--                    "-->
                    <!--                  >-->
                    <!--                    <v-select-->
                    <!--                      v-model="bookingForm.sales_team_id"-->
                    <!--                      label="Sales Team"-->
                    <!--                      :items="salesTeams"-->
                    <!--                      item-text="name"-->
                    <!--                      item-value="id"-->
                    <!--                      outlined-->
                    <!--                      :menu-props="{ bottom: true, offsetY: true }"-->
                    <!--                      background-color="#fff"-->
                    <!--                    ></v-select>-->
                    <!--                  </v-col>-->
                    <!--                </v-row>-->
                  </div>
                  <v-expansion-panels v-model="productDetailsExpansion" class="mt-2 overflow-hidden" mandatory>
                    <v-expansion-panel active-class="active">
                      <v-expansion-panel-header class="panel-header" disable-icon-rotate hide-actions>
                        <template v-slot:default="{ open }">
                          <div class="d-flex justify-space-between align-center">
                            <h3 class="text-base">1. Customer & Product Details</h3>
                            <div class="d-flex align-center gap-x-2">
                              <MinusIcon v-if="open"/>
                              <PlusIcon v-else color="#0F2A4D"/>
                            </div>
                          </div>
                        </template>
                      </v-expansion-panel-header>
                      <v-expansion-panel-content class="px-4 panel-content">
                        <v-row>
                          <v-col cols="12" md="8">
                            <customer-booking-form
                                :id="id"
                                :key="customerFormRefresh"
                                :companies="companies"
                                :countries="countries"
                                :disablePromotion="disablePromotion"
                                :idProofTypes="idProofTypes"
                                :order_id="order_id"
                                :perCapacity="perCapacity"
                                :promotions="promotions"
                                :singleBookingForm="bookingForm"
                                :tags="tags"
                                :comp_index="'1000'"
                                :venue_service_id="venue_service_id"
                                @clearBenefit="(data) => clearBenefit()"
                                @customerTypeChange="customerTypeChange($event, null)"
                                @setCustomerData="(data) => setCustomerData(data)"
                                @setMemberData="(data) => setMemberData(data)"
                                @verifyBenefit="(type) => verifyBenefit(type)"
                            />
                            <div
                                v-for="(addOns, index) in bookingCustomersAddons"
                                :key="index"
                                class="rounded-lg mt-4 relative"
                            >
                              <customer-booking-form
                                  :id="id"
                                  :addOn="1"
                                  :companies="companies"
                                  :countries="countries"
                                  :disablePromotion="disablePromotion"
                                  :idProofTypes="idProofTypes"
                                  :order_id="order_id"
                                  :perCapacity="perCapacity"
                                  :promotions="promotions"
                                  :singleBookingForm="addOns"
                                  :tags="tags"
                                  :comp_index="`index_primary_add_on_${index}`"
                                  :venue_service_id="venue_service_id"
                                  @customerTypeChange="customerTypeChange($event, null)"
                                  @removeCustomerAdd="
                        (data) => removeAddonCustomer(index, null)
                      "
                                  @setCustomerData="
                        (data) => setCustomerDataAddon(data, index)
                      "
                              />
                            </div>
                            <div
                                v-if="
                      perCapacity == 1 && bookingFormAdded < bookingFormTotal
                    "
                                class="add_btn pb-4"
                            >
                              <v-tooltip bottom>
                                <template v-slot:activator="{ on, attrs }">
                                  <v-btn
                                      color="teal"
                                      dark
                                      fab
                                      v-bind="attrs"
                                      x-small
                                      @click="addBookingFormCustomer"
                                      v-on="on"
                                  >
                                    <v-icon small>mdi-plus-circle</v-icon>
                                  </v-btn>
                                </template>
                                Add
                              </v-tooltip>
                            </div>
                            <v-card
                                v-if="uploadedAttachments.length > 0"
                                class="mt-2"
                                color="#edf9ff"
                                outlined
                                style="border: 1px #ccc solid"
                            >
                              <v-card-text>
                                <div class="titles">Uploaded Attachments</div>
                                <template v-for="doc in uploadedAttachments">
                                  <v-chip
                                      v-if="doc.document_path"
                                      :key="doc.id"
                                      color="grey accent-4 mr-2 mb-2"
                                      dark
                                      label
                                      small
                                      @click="openFile(doc.document_path)"
                                  >
                                    {{ doc.document_name }}
                                  </v-chip>
                                </template>
                              </v-card-text>
                            </v-card>
                            <div
                                v-if="venueServiceDocuments.length > 0"
                                class="bg-white bordered rounded-lg pb-4 mt-8"
                            >
                              <div
                                  class="d-flex justify-space-between align-center bg-neon opacity-10 border-bottom pa-4">
                                <div class="title-text">Documents</div>
                              </div>
                              <div class="pa-4">
                                <template v-for="doc in venueServiceDocuments">
                                  <v-chip
                                      v-if="doc.file_path"
                                      :key="doc.id"
                                      color="grey accent-4 mr-2"
                                      dark
                                      label
                                      small
                                      @click="openFile(doc.file_path)"
                                  >
                                    {{ doc.name }}
                                  </v-chip>
                                </template>
                              </div>
                            </div>
                          </v-col>
                          <v-col cols="12" md="4">
                            <ProductSelection
                                :key="refreshComponent"
                                :bookingForm="bookingForm"
                                :categories="categories"
                                :categoriesList="categoriesList"
                                :currentOrderProducts="currentOrderProducts"
                                :deletedProducts="deletedProducts"
                                :productCatId="productCategoryId"
                                :productCombinations="productCombinations"
                                :products="bookingForm.products"
                                :repeatId="repeatId"
                                :taxTypes="taxTypes"
                                :venueServiceId="venue_service_id"
                                @chooseRentalCombination="chooseRentalCombination"
                                @deleteProduct="deleteProduct"
                                @removeProduct="(productIndex) => removeProduct(productIndex)"
                                @setCurrentOrderProducts="setCurrentOrderProducts"
                                @setCustomerProduct="(data) => setCustomerProduct(null, data)"
                            />
                          </v-col>
                        </v-row>
                        <repeat-bookingPayment
                            v-if="repeatId"
                            :openProduct="bookingWithOpenProduct"
                            :repeatId="repeatId"
                            v-bind="bookingForm"
                            @repeatDatesForPayment="repeatDatesForPaymentChange"
                        ></repeat-bookingPayment>
                      </v-expansion-panel-content>
                    </v-expansion-panel>
                    <template v-if="!order_id">
                      <v-expansion-panel v-for="(customer, index) in attendanceCustomers" :key="index" active-class="active" class="mt-4">
                        <v-expansion-panel-header class="panel-header" disable-icon-rotate hide-actions>
                          <template v-slot:default="{ open }">
                            <div class="d-flex justify-space-between align-center">
                              <h3 class="text-base">{{ index + 2 }}. Customer & Product Details</h3>
                              <div class="d-flex align-center gap-x-2">
                                <v-tooltip v-if="open" bottom>
                                  <template v-slot:activator="{ on, attrs }">
                                    <v-btn
                                        fab
                                        v-bind="attrs"
                                        x-small
                                        @click="removeCustomer(index)"
                                        v-on="on"
                                    >
                                      <DeleteIcon/>
                                    </v-btn>
                                  </template>
                                  Delete
                                </v-tooltip>
                                <MinusIcon v-if="open"/>
                                <PlusIcon v-else color="#0F2A4D"/>
                              </div>
                            </div>
                          </template>
                        </v-expansion-panel-header>
                        <v-expansion-panel-content>
                          <div
                              class="my-4 px-4"
                          >
                            <v-row class="pl-2">
                              <v-col class="rounded-lg p-0" md="7">

                                <customer-booking-form
                                    :id="id"
                                    :key="index"
                                    :comp_index="`index_secondary_${index}`"
                                    :companies="companies"
                                    :countries="countries"
                                    :idProofTypes="idProofTypes"
                                    :order_id="order_id"
                                    :perCapacity="perCapacity"
                                    :promotions="promotions"
                                    :singleBookingForm="customer"
                                    :tags="tags"
                                    :venue_service_id="venue_service_id"
                                    @clearBenefit="(data) => clearBenefit(index)"
                                    @customerTypeChange="customerTypeChange($event, index)"
                                    @setCustomerData="
                          (data) => setCustomerData(data, index)
                        "
                                    @setMemberData="(data) => setMemberData(data, index)"
                                    @verifyBenefit="(type) => verifyBenefit(type, index)"
                                />

                                <v-card
                                    v-for="(addOns, index1) in attendanceCustomersAddons[
                          index
                        ]"
                                    :key="index1 + 'hi'"
                                    class="mt-5 mb-6"
                                    elevation="0"
                                >
                                  <customer-booking-form
                                      :id="id"
                                      :addOn="order_id ? 2 : 1"
                                      :companies="companies"
                                      :countries="countries"
                                      :disablePromotion="disablePromotion"
                                      :idProofTypes="idProofTypes"
                                      :order_id="order_id"
                                      :perCapacity="perCapacity"
                                      :promotions="promotions"
                                      :singleBookingForm="addOns"
                                      :tags="tags"
                                      :comp_index="`index_secondary_${index}_add_on_${index1}`"
                                      :venue_service_id="venue_service_id"
                                      @customerTypeChange="customerTypeChange($event, null)"
                                      @removeCustomerAdd="
                            (data) => removeAddonCustomer(index1, index)
                          "
                                      @setCustomerData="
                            (data) => setCustomerDataAddon(data, index, index1)
                          "
                                  />
                                </v-card>
                                <div
                                    v-if="
                          perCapacity == 1 &&
                            attendanceCustomerAdded[index] <
                              attendanceCustomersTotal[index]
                        "
                                    class="add_btn pb-4"
                                >
                                  <v-tooltip bottom>
                                    <template v-slot:activator="{ on, attrs }">
                                      <v-btn
                                          color="teal"
                                          dark
                                          fab
                                          v-bind="attrs"
                                          x-small
                                          @click="addAttendanceCustomer(index)"
                                          v-on="on"
                                      >
                                        <v-icon small>mdi-plus-circle</v-icon>
                                      </v-btn>
                                    </template>
                                    Add
                                  </v-tooltip>
                                </div>
                              </v-col>
                              <v-col md="5">
                                <ProductSelection
                                    :key="index"
                                    :bookingForm="customer"
                                    :categories="categories"
                                    :categoriesList="categoriesList"
                                    :currentOrderProducts="currentOrderProducts"
                                    :deletedProducts="deletedProducts"
                                    :productCatId="customer.productCategoryId"
                                    :productCombinations="productCombinations"
                                    :products="customer.products"
                                    :taxTypes="taxTypes"
                                    :venueServiceId="venue_service_id"
                                    className=""
                                    @chooseRentalCombination="chooseRentalCombination"
                                    @deleteProduct="deleteProduct"
                                    @removeProduct="
                              (productIndex) =>
                                removeProduct(productIndex, index)
                            "
                                    @setCurrentOrderProducts="setCurrentOrderProducts"
                                    @setCustomerProduct="
                              (data) => setCustomerProduct(index, data)
                            "/>
                              </v-col>
                            </v-row>
                          </div>
                        </v-expansion-panel-content>
                      </v-expansion-panel>
                    </template>
                  </v-expansion-panels>
                  <div v-if="addAttandanceBtn && bookedCapacity + 1 < capacity && !order_id" class="mt-4">
                    <v-btn
                        color="blue-color"
                        dark
                        @click="addAttandance"
                    >
                      <strong>+</strong> Add Guest
                    </v-btn>
                  </div>
                </v-card-text>
              </v-col>
              <v-col class="d-flex booking-sidebar-right pt-0" cols="3" :style="{'maxHeight':this.maxHeight+20+'px'}">
                <div
                    class="bg-white shadow overflow-x-hidden rounded-lg flex-grow-1 d-flex flex-column"
                >
                  <div class="d-flex justify-space-between align-center bg-neon opacity-10 border-bottom pa-4">
                    <div class="title-text">Added Products</div>
                    <v-btn @click="close" elevation="0" text class="no-hover-effect pr-0">
                      <ModalCloseIcon />
                    </v-btn>
                  </div>
                  <div class="p-4 d-flex flex-column justify-space-between relative flex-grow-1" style="min-height:300px;">
                    <div class="overflow-y-auto product-box" >
                      <div v-if="bookingForm.products && bookingForm.products.length > 0">
                        <p v-if="attendanceCustomers.length > 0" class="mb-1">
                          {{ bookingForm.first_name || "Customer" }}
                        </p>
                        <div v-for="(product,productIndex) in bookingForm.products" :key="productIndex"
                             class="bg-light-neon mb-4 p-2 rounded-lg">
                          <div>
                            <div class="d-flex justify-space-between align-start mb-1">
                              <div>
                                <label>
                                  Product
                                </label>
                                <p class="font-medium text-black mb-1">
                                  {{ product.name }}
                                </p>
                              </div>
                              <button style="min-width: 28px;" class="mt-2" type="button" @click="removeProduct(productIndex)">
                                <DeleteProductIcon stroke="#ADADAD"/>
                              </button>
                            </div>
                            <div class="d-flex justify-space-between">
                              <div>
                                <label>
                                  Quantity
                                </label>
                                <ManageQuantity
                                    :categories="categories"
                                    :product="product"
                                    :key="product.quantity"
                                    :per-capacity="perCapacity"
                                    @update:product="prod => {
                                      product = prod;
                                      bookingForm.total_price = bookingForm.products.reduce(
                                            (a, b) => a + parseFloat(b.total_price),
                                            0
                                        )
                                      $forceUpdate()
                                    }"
                                    :bookingId="bookingForm.id"
                                    :repeatId="repeatId"
                                />
                              </div>
                              <div>
                                <label>
                                  Price
                                </label>
                                <p class="font-medium text-black mb-1" :key="product.quantity">
                                  {{ product.total_price | toCurrency }}
                                  <span
                                      v-if="product.discount != null"
                                      class="text-decoration-line-through pl-1"
                                  >
                                    {{ product.discount.actual_total | toCurrency }}</span
                                  >
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <template v-if="attendanceCustomers && attendanceCustomers.length > 0">
                        <div v-for="(customer,index) in attendanceCustomers" :key="index">
                          <p v-if="customer.products.length > 0" class="mb-1">
                            {{ customer.first_name || ("Customer " +(index + 2)) }}
                          </p>
                          <div v-for="(product,productIndex) in customer.products" :key="productIndex"
                               class="bg-light-neon mb-4 p-2 rounded-lg">
                            <div>
                              <div class="d-flex justify-space-between mb-2">
                                <div>
                                  <label>
                                    Product
                                  </label>
                                  <p class="font-medium text-black">
                                    {{ product.name }}
                                  </p>
                                </div>
                                <button style="min-width: 28px;" type="button" @click="removeProduct(productIndex,index)">
                                  <DeleteProductIcon stroke="#ADADAD"/>
                                </button>
                              </div>
                              <div class="d-flex justify-space-between">
                                <div>
                                  <label>
                                    Quantity
                                  </label>

                                  <ManageQuantity
                                      :categories="categories"
                                      :product="product"
                                      :key="product.quantity"
                                      :per-capacity="perCapacity"
                                      @update:product="prod => {
                                      product = prod;
                                      customer.total_price = customer.products.reduce(
                                            (a, b) => a + parseFloat(b.total_price),
                                            0
                                        )
                                      $forceUpdate()
                                    }"
                                      :bookingId="bookingForm.id"
                                      :repeatId="repeatId"
                                  />
                                </div>
                                <div>
                                  <label>
                                    Price
                                  </label>
                                  <p class="font-medium text-black">
                                    {{ product.total_price | toCurrency }}
                                    <span
                                        v-if="product.discount != null"
                                        class="text-decoration-line-through pl-1"
                                    >
                                    {{ product.discount.actual_total | toCurrency }}</span
                                    >
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </template>
                    </div>
                    <div class="order-summary-box pt-8">
                      <v-expansion-panels>
                        <v-expansion-panel class="panel">
                          <v-expansion-panel-header class="p-0 panel-header" style="min-height: 36px !important;" hide-actions>
                            <template v-slot:default="{ open }">
                              <div class="d-flex justify-space-between align-center px-2">
                                <div class="text-sm">Order Note</div>
                                <div class="d-flex align-center gap-x-2">
                                  <ClearMinusIcon v-if="open"/>
                                  <ClearPlusIcon v-else/>
                                </div>
                              </div>
                            </template>
                          </v-expansion-panel-header>
                          <v-expansion-panel-content class="pa-2">
                            <v-textarea
                                v-model="bookingForm.order_notes"
                                background-color="#fff"
                                class="q-autocomplete shadow-0"
                                clearable
                                dense
                                hide-details="auto"
                                outlined
                                rows="1"
                            ></v-textarea>
                          </v-expansion-panel-content>
                        </v-expansion-panel>
                      </v-expansion-panels>
                      <v-row class="m-0">
                        <v-col
                            v-if="promotions.length > 0 && perCapacity == 0"
                        >
                          <label for="">Promotions</label>
                          <v-autocomplete
                              v-if="this.bookingForm.card_number == null"
                              v-model="bookingForm.promotion_code"
                              :items="[{ name: 'None', promotion_code: null },...promotions]"
                              :readonly="disablePromotion"
                              class="q-autocomplete shadow-0"
                              dense
                              hide-details="auto"
                              item-text="name"
                              item-value="promotion_code"
                              label=""
                              outlined
                              @change="verifyBenefit('promotion')"
                          >
                          </v-autocomplete>
                        </v-col>
                        <v-col
                            v-if="
                      checkReadPermission($modules.salesTeam.management.slug)
                    "
                        >
                          <label for="">Sales Team</label>
                          <v-select
                              v-model="bookingForm.sales_team_id"
                              :items="salesTeams"
                              :menu-props="{ bottom: true, offsetY: true }"
                              background-color="#fff"
                              class="q-autocomplete shadow-0"
                              dense
                              hide-details="auto"
                              item-text="name"
                              item-value="id"
                              label=""
                              outlined
                          ></v-select>
                        </v-col>
                      </v-row>
                      <div class="d-flex justify-space-between align-center mt-4">
                        <p class="mb-1">
                          Total Price
                        </p>
                        <p class="font-bold text-lg mb-1" :key="totalPrice">
                          {{ totalPrice | toCurrency }}
                        </p>
                      </div>
                      <div class="mt-2 d-flex align-center">
                        <v-menu
                            v-if="id>0"
                            content-class="q-menu"
                            offset-y
                        >
                          <template v-slot:activator="{ on, attrs }">
                            <v-btn
                                :ripple="false"
                                class="text_capitalize no-hover-effect bg-neon opacity-10 rounded-lg pa-0 mr-4"
                                elevation="0"
                                height="40"
                                style="min-width: 40px !important;"
                                v-bind="attrs"
                                v-on="on"
                            >
                              <DotsIcon/>
                            </v-btn>
                          </template>

                          <v-list>
                            <v-list-item
                                v-if="
                                    bookingForm.is_booking_approved == 1 &&
                                    (bookingForm.status_id == 5 || bookingForm.status_id == 7)
                                  "
                                @click="showInvoice"
                            >
                              <SvgIcon class="text-teal font-medium text-sm gap-x-2" text="Invoice"/>
                            </v-list-item>

                            <v-list-item
                                v-if="repeatId && !bookingWithOpenProduct"
                                :disabled="repeatId && !isEnableRepeatBookingPayment"
                                @click="showRepeatPaymentsConfirmation"
                            >
                              <SvgIcon class="text-teal font-medium text-sm gap-x-2" text="Pay"/>
                            </v-list-item>

                            <v-list-item
                                v-if="
                                    bookingForm.is_booking_approved == 0 &&
                                    (bookingForm.status_id == 5 || bookingForm.status_id == 7) &&
                                    checkWritePermission($modules.facility.approval.slug)
                                  "
                                @click="approveBooking"
                            >
                              <SvgIcon class="text-green font-medium text-sm gap-x-2" text="Approve"/>
                            </v-list-item>
                            <v-list-item
                                v-if="perCapacity != 1 && bookingForm.status_id == 5 && !fullDay && checkWritePermission($modules.schedule.management.slug)"
                                :disabled="repeatId && !isEnableRepeateBookingReschedule"
                                @click="reschedule()"
                            >
                              <SvgIcon class="text-green font-medium text-sm gap-x-2" text="Reschedule"/>
                            </v-list-item>
                            <v-list-item v-if="!repeatId && bookingForm.status_id == 5" @click="confirmCancel">
                              <SvgIcon class="text-red font-medium text-sm gap-x-2" text="Cancel Booking"/>
                            </v-list-item>

                            <v-list-item v-if="repeatId && bookingForm.status_id == 5" @click="cancelRepeatBooking">
                              <SvgIcon class="text-red font-medium text-sm gap-x-2" text="Cancel Booking"/>
                            </v-list-item>

                            <v-list-item
                                v-show="
                                    !repeatId &&
                                    bookingForm.status_id == 4 &&
                                    (current_group_customer.check_in_time == null ||
                                      current_group_customer.check_out_time == null)
                                  "
                                @click="checkInAndOut(current_group_customer.group_customer_id)"
                            >
                              <SvgIcon
                                  :class="{
                                    'text-red': current_group_customer.check_in_and_out == 'OUT',
                                    'text-green': !current_group_customer.check_in_and_out == 'OUT',
                                    'font-medium text-sm gap-x-2': true
                                  }"
                                  :text="current_group_customer.check_in_and_out? 'Check ' + (current_group_customer.check_in_and_out == 'OUT' ? 'Out' : 'In'): 'Check In/Out'"
                              />
                            </v-list-item>

                          </v-list>
                        </v-menu>

                        <v-btn
                            v-if="checkWritePermission($modules.schedule.management.slug)"
                            class="white--text teal-color rounded-lg flex-grow-1"
                            height="40"
                            text
                            @click="confirmAddOrEditBooking"
                        >
                          <span class="font-bold text-lg">
                            {{ order_id ? 'Update Reservation' : 'Confirm Reservation' }}
                          </span>
                        </v-btn>
                      </div>
                    </div>
                  </div>
                </div>
              </v-col>
            </v-row>
            <!--          <v-card-actions>-->
            <!--            <v-btn-->
            <!--                v-if="perCapacity != 1 && bookingForm.status_id == 5 && !fullDay && checkWritePermission($modules.schedule.management.slug)"-->
            <!--                :disabled="repeatId && !isEnableRepeateBookingReschedule"-->
            <!--                class="ma-2 yellow-color"-->
            <!--                text-->
            <!--                @click="reschedule()"-->
            <!--            >Reschedule-->
            <!--            </v-btn>-->
            <!--          </v-card-actions>-->
          </div>
        </v-form>
    </v-dialog>
    <confirm-model
        v-bind="confirmModel"
        @close="confirmModel.id = null"
        @confirm="confirmActions"
    >
      <template v-if="this.id && this.confirmModel.type === 'update' && typeof confirmModel.data.notify_customers !== 'undefined'"
                #content>
        <v-row>
          <v-col cols="6">
            <v-switch
                v-model="notify_customers"
                class="mx-0 my-0"
                dense
                hide-details="auto"
                label="Notify Customers"
            ></v-switch>
          </v-col>
        </v-row>
      </template>
    </confirm-model>
    <product-combination
        :productCombinations="productCombinations"
        :selectedCombinationPrice="selectedCombinationPrice"
        :showCombinations="showCombinationDialog"
        @changeCombination="changeRentalProducts"
        @close="showCombinationDialog = false"
    >
    </product-combination>

    <repeat-order-payments
        v-if="enableRepeatOrderPay"
        :date="date"
        :orderId="order_id"
        :payerCustomerList="payerCustomerList"
        :promotion_code="bookingForm.promotion_code"
        :repeatBookingIds="repeatBookingIds"
        :show="enableRepeatOrderPay"
        :wallet="wallet"
        v-bind="bookingForm"
        @close="enableRepeatOrderPay = false"
        @payed="$emit('booked'), (enableRepeatOrderPay = false)"
    ></repeat-order-payments>
    <facility-maintenance
        :endDate="date"
        :endTime="end_time"
        :facilityId="maintenanceId"
        :startDate="date"
        :startTime="start_time"
        :timeIncrement="increment"
        :venueServiceId="venue_service_id"
        @close="closeMaintenance"
        @save="saveMaintenance"
    ></facility-maintenance>
  </div>
</template>
<script>
import RepeatBooking from '@/components/Schedule/Facility/RepeatBooking'
import RepeatBookingPayment from '@/components/Schedule/Facility/RepeatBookingPayment'
import ProductCombination from '@/components/Schedule/Facility/ProductCombination'
import moment, { now } from 'moment'
import bookingFields from '@/mixins/bookingFieldValidation'
import RepeatOrderPayments from '@/components/Order/RepeatOrderPayments.vue'
import FacilityMaintenance from '@/components/Facility/FacilityMaintenance'
import CustomerBookingForm from './CustomerBookingForm.vue'
import ProductSelection from '@/components/Schedule/Facility/ProductSelection'
import MaintenanceIcon from '@/assets/images/facilities/maintenance.svg'
import RacingIcon from '@/assets/images/facilities/racing.svg'
import MinusIcon from '@/assets/images/misc/white-minus-icon.svg'
import PlusIcon from '@/assets/images/misc/white-plus-icon.svg'
import ClearMinusIcon from '@/assets/images/misc/minus-icon.svg'
import ClearPlusIcon from '@/assets/images/misc/plus-icon.svg'
import DeleteIcon from '@/assets/images/misc/delete-bg-icon.svg'
import DeleteProductIcon from '@/assets/images/misc/delete-icon.svg'
import ModalCloseIcon from '@/assets/images/misc/modal-close.svg'
import DotsIcon from '@/assets/images/misc/h-options.svg'
import SvgIcon from "@/components/Image/SvgIcon.vue";
import ManageQuantity from '@/views/Facility/Rentals/ManageQuantity.vue'

export default {
  props: {
    showBookingForm: { type: Boolean },
    start_time: { type: String },
    end_time: { type: String },
    date: { type: String },
    facility_id: { type: Number },
    is_enable_limit_product: { type: Number,default: 0 },
    max_base_product: { type: Number,default: null },
    order_id: { type: Number },
    id: { type: Number, default: 0 },
    facility_name: { type: String },
    venue_service_id: { type: Number },
    service: { type: String },
    perCapacity: { type: Number },
    increment: { type: Number },
    minBookingTime: { type: Number },
    openingTime: { type: String },
    closingTime: { type: String },

  },
  components: {
    ManageQuantity,
    ModalCloseIcon,
    DeleteIcon,
    DeleteProductIcon,
    PlusIcon,
    MinusIcon,
    ClearMinusIcon,
    ClearPlusIcon,
    DotsIcon,
    RepeatBooking,
    ProductCombination,
    RepeatBookingPayment,
    RepeatOrderPayments,
    FacilityMaintenance,
    CustomerBookingForm,
    ProductSelection,
    MaintenanceIcon,
    RacingIcon,
    SvgIcon
  },
  mixins: [bookingFields],
  data () {
    return {
      allowTrainerAssignment:false,
      enableOvernightBooking: false,
      customerFormRefresh: now(),
      refreshComponent: 0,
      currentRepeatDates: null,
      bookingForm: {
        attendance: true,
        attendance_count: 1,
        opt_marketing: false,
        total_price: 0,
        price: 0,
        discount: null,
        promotion_code: null,
        products:[]
      },
      bookingFormTotal: 0,
      bookingFormAdded: 1,
      bookingCustomersAddons: [],
      selectedProduct: {},
      productCategoryId: null,
      wallet: {
        products: null,
        cash: null,
      },
      // webcamDialog: false,
      allowed_trainers:[],
      endTimes: [],
      categories: [],
      companies: [],
      valid: false,
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      repeatId: null,
      productCombinations: [],
      showCombinationDialog: false,
      selectedCombinationPrice: 0,
      isEnableRepeatBookingPayment: false,
      isEnableRepeateBookingReschedule: false,
      repeatDatesForPayments: null,
      enableRepeatOrderPay: false,
      payerCustomerList: [],
      repeatBookingIds: [],
      currentOrderProducts: [],
      attendanceData: {},
      capacity: 0,
      attendies: 0,
      bookedCapacity: 1,
      totalParticipants: 0,
      attendanceSwitch: false,
      uploadedAttachments: [],
      attendanceCustomers: [
        // {
        //   opt_marketing: false,
        //   productCategoryId: null,
        //   quantity: 1,
        //   price: 0,
        //   total_price: 0,
        //   selectedProduct: { quantity: 1, price: 0 },
        //   products: new Array(),
        //   discount: null,
        //   promotion_code: null,
        // },
      ],
      attendanceCustomersTotal: [0],
      attendanceCustomerAdded: [1],
      attendanceCustomersAddons: [[]],
      editFlag: false,
      pastTime: false,
      maintenanceId: null,
      categoriesList: [
        { name: 'All', id: null },
        { name: 'Open Product', id: -1 },
      ],
      disablePromotion: false,
      bookingWithOpenProduct: false,
      isEmiratesIdCheck: false,
      deletedProducts: [],
      addAttandanceBtn: true,
      current_group_customer: {},
      fullDay: false,
      facilities: [],
      facilityStartTime: null,
      facilityEndTime: null,
      notify_customers: false,
      productDetailsExpansion: 0,
      openCustomerPanel: 0,
      maxHeight: 830
    }
  },
  watch: {
    showBookingForm (val) {
      this.customerFormRefresh = now() + 11
      this.disablePromotion = false
      this.repeatId = null
      this.isEnableRepeateBookingReschedule = null
      this.refreshComponent = now()
      this.attendanceCustomers = []
      this.attendanceData = []
      this.uploadedAttachments = []
      this.currentOrderProducts = []
      this.currentRepeatDates = null
      this.attendanceSwitch = false
      this.bookedCapacity = 1
      this.addAttandanceBtn = true
      this.productCategoryId = null
      this.selectedProduct = { rental: false }
      this.attendies = 0
      this.deletedProducts = []
      if (val === true) {
        this.calculateHeight();
        this.checkEnableOvernightBooking()
        this.bookingForm = {
          start_time: this.start_time,
          end_time: this.end_time,
          date: this.date,
          facility_id: this.facility_id,
          venue_service_id: this.venue_service_id,
          attendance_count: 1,
          price: 0,
          total_price: 0,
          discount: null,
          promotion_code: null,
          is_enable_limit_product: this.is_enable_limit_product,
          max_base_product: this.max_base_product,
        }
        if (this.id > 0) {
          this.bookingForm.id = this.id
          this.bookingForm.order_id = this.order_id
        }
        this.$store.dispatch('loadPromotions', {
          date: this.date,
          venue_service_id: this.venue_service_id,
          product_type: 'Facility',
        })
        if (this.venue_service_id) {
          this.getFacilities()
        }
        // this.getCustomerWallets();
        this.setFieldConfigurations();
        this.getFacilityUtils();
        this.getAssignableTrainers();
      }

    },
  },
  mounted () {
    if (this.$store.getters.getCountries.status == false) {
      this.$store.dispatch('loadCountries')
    }
    if (this.$store.getters.getIdProofTypes.status == false) {
      this.$store.dispatch('loadIDProofTypes')
    }
    if (this.$store.getters.getTaxTypes.status == false) {
      this.$store.dispatch('loadTaxTypes')
    }
    if (this.$store.getters.getPaymentMethods.status == false) {
      this.$store.dispatch('loadPaymentMethods', 'normal')
    }
    if (this.$store.getters.getTags.status == false) {
      this.$store.dispatch('loadTags')
    }
    if (this.$store.getters.getVenueServices.status == false) {
      this.$store.dispatch('loadVenueServices').then(() => {
        if (this.venue_service_id) {
          this.getFacilities()
        }
      })
    }
    this.checkPermission = this.checkExportPermission(
        this.$modules.salesTeam.dashboard.slug
    )
    if (this.checkPermission) {
      this.$store.dispatch('loadSalesTeams', 'Facility')
      this.$forceUpdate()
    }
   this.calculateHeight();
  },
  computed: {
    countries () {
      return this.$store.getters.getCountries.data
    },
    promotions () {
      return this.$store.getters.getPromotions.data
    },
    idProofTypes () {
      return this.$store.getters.getIdProofTypes.data.filter(i => !['Scanned document', 'Unified ID'].includes(i.name))
    },
    taxTypes () {
      return this.$store.getters.getTaxTypes.data
    },
    tags () {
      return this.$store.getters.getTags.data
    },
    venueServiceConfiguration () {
      return this.$store.getters.getConfigurationByVenueServiceId(
          this.venue_service_id
      )
    },
    isRaceFormatEnabled(){
      return this.venueServiceConfiguration && this.venueServiceConfiguration.race_formats && this.venueServiceConfiguration.race_formats.length > 0;
    },
    salesTeams () {
      return this.$store.getters.getSalesTeams.data
    },
    startTimes () {
      let times = []
      let currentTime = null
      const startTime = this.facilityStartTime ? this.facilityStartTime : this.start_time
      const endTime = this.facilityEndTime ? this.facilityEndTime : this.end_time
      currentTime = moment(this.date + ' ' + startTime, 'YYYY-MM-DD HH:mm:ss')
      let closingTime = moment(this.date + ' ' + endTime, 'YYYY-MM-DD HH:mm:ss')
      while (currentTime.isBefore(closingTime)) {
        times.push({
          formatted: currentTime.format('hh:mm a'),
          time: currentTime.format('HH:mm:ss'),
        })
        currentTime.add(5, 'minutes')
      }
      // times.pop();
     //console.log("all times",[...times]);
      return times
    },
    totalPrice () {
      let amount = this.bookingForm.total_price || 0;
      this.attendanceCustomers.forEach(customer => {
        amount += customer.total_price;
      })
      return amount;
    },
  },
  methods: {
    getAssignableTrainers() {
      if(!this.venueServiceConfiguration.allow_trainer_assignment || !this.allowTrainerAssignment){
        //console.log('trainers not allowed');
        this.allowed_trainers = [];
        return;
      }
      let url = `venues/trainers/short?venue_service_ids[0]=${this.venue_service_id}`;
      this.showLoader("Loading Trainers");
      this.$http
          .get(`${url}`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.allowed_trainers = response.data.data;
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          }).finally(()=> {
        this.hideLoader();
      });
    },
    async getFacilityTimings() {
      let timings = { start_time: null, end_time: null };
      this.showLoader('Loading');
      try {
        const response = await this.$http.get(`venues/facilities/get-min-time?facility_id=${this.facility_id}&date=${this.date}&same_day=1`);
        if (response.status === 200 && response.data.status === true) {
          timings.start_time = response.data.start_time;
          timings.end_time = response.data.end_time;
        }
      } catch (error) {
        console.error('getFacilityTimings failed', error);
      } finally {
        this.hideLoader();
      }
      return timings;
    },
    async addOverNightBookingTimes (data) {
      let times = await this.getFacilityTimings()
      let minStartTime = null

      if (times && times.end_time) {
        minStartTime = times.start_time.start_time
      } else {
        minStartTime = data.facility_rentals.reduce((min, obj) => {
          const start_time = moment(obj.start_time, 'HH:mm:ss')
          return start_time.isAfter(moment(min, 'HH:mm:ss')) ? start_time.format('HH:mm:ss') : min
        }, moment('00:00:00', 'HH:mm:ss'))
      }

      let newTimesots = this.generateTimeSlots(minStartTime, this.increment, this.minBookingTime, this.bookingForm.start_time)
      this.endTimes.push(...newTimesots)
    },
    generateTimeSlots (minStartTime, increment, minBookingTime, bookingStartTime) {
      const timeSlots = []
      let currentTime = moment(minStartTime, 'HH:mm:ss')
      const bookingEndTime = moment(bookingStartTime, 'HH:mm:ss').subtract(minBookingTime - increment, 'minutes')
      let key = 0
      while (currentTime.isBefore(bookingEndTime)) {
        if (key !== 0) {
          timeSlots.push({
            formatted: currentTime.format('hh:mm a') + '<span style="color: red">+1</span>',
            time: currentTime.format('HH:mm:ss'),
          })
        } else {
          key++
        }
        currentTime.add(increment, 'minutes')
      }
      return timeSlots
    },
    checkEnableOvernightBooking () {
      if(this.venueServiceConfiguration.enable_over_night_booking){
        this.enableOvernightBooking = true;
      }else{
        this.enableOvernightBooking = false;
      }
      if(this.venueServiceConfiguration.allow_trainer_assignment){
        this.allowTrainerAssignment = true;
      }else{
        this.allowTrainerAssignment = false;
      }
    },
    checkInAndOut (id) {
      this.showLoader('Loading')
      this.$http
          .get(`venues/facilities/bookings/check-in-and-out/${id}`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.hideLoader()
              this.getBookingDetails()
            }
          })
          .catch((error) => {
            this.hideLoader()
            this.errorChecker(error)
          })
    },
    getCustomerWallets () {
      if (!this.order_id) {
        return
      }
      this.$http
          .get(
              `venues/customers/products-wallet/get-customer-all/${this.order_id}`
          )
          .then((response) => {
            if (response.status == 200 && response.data.status) {
              this.hideLoader()
              let data = response.data
              this.wallet.cash = data.cash
              this.wallet.products = data.products
            }
          })
          .catch((error) => {
            this.hideLoader()
            this.errorChecker(error)
          })
    },
    refreshCustomersMain () {
      if (this.bookingFormTotal) {
        this.bookingCustomersAddons.splice(this.bookingFormTotal - 1)
      } else {
        this.bookingCustomersAddons = []
      }
    },
    refreshCustomersAttendance (index) {
      this.attendanceCustomersAddons[index].splice(
          this.attendanceCustomersTotal[index] - 1
      )
    },
    removeAddonCustomer (index, key) {
      if (key == null) {
        this.bookingCustomersAddons.splice(index, 1)
        this.bookingFormAdded--
      } else {
        this.attendanceCustomersAddons[key].splice(index, 1)
        this.attendanceCustomerAdded[key]--
      }
    },

    updateBookingFormQuantity () {
      let old = this.bookingFormTotal
      let max = this.bookingForm.products.reduce(
          (acc, num) =>
              acc +
              (num.rental == true
                  ? parseInt(num.quantity) * parseInt(num.participant_count)
                  : 0),
          0
      )
      // console.log("max");
      // console.log(max);
      if (old > max && max < this.bookingFormAdded) {
        this.bookingFormAdded = max + 1
      }

      this.bookingFormTotal = max
    },

    updateAttendanceFormQuantity (index) {
      let old = this.attendanceCustomersTotal[index] ?? 0
      let max = 0
      if (this.attendanceCustomers[index].products.length) {
        max = this.attendanceCustomers[index].products.reduce(
            (acc, num) =>
                acc +
                (num.rental == true
                    ? parseInt(num.quantity) * parseInt(num.participant_count)
                    : 0),
            0
        )
      }
      if (old > max && max < this.attendanceCustomerAdded[index]) {
        this.attendanceCustomerAdded[index] = max + 1
      }
      this.attendanceCustomersTotal[index] = max
    },

    addBookingFormCustomer () {
      this.bookingCustomersAddons.push({
        customer_id: null,
        attendance: false,
        attendance_count: 1,
        opt_marketing: false,
        total_price: 0,
        price: 0,
        discount: null,
        promotion_code: null,
        additional_fields:[]
      })
      this.bookingFormAdded++
      if (this.venueServiceConfiguration.auto_fill_customers) {
        this.autoFillCustomer()
      }
    },
    autoFillCustomer () {
      if (!this.bookingForm.name) {
        return
      }
      let index =
          this.bookingCustomersAddons.length > 0
              ? this.bookingCustomersAddons.length - 1
              : null
      if (index >= 0) {
        let data = {
          customer_id: null,
          name: this.bookingForm.name + ' Guest #' + (index + 1),
          mobile: this.bookingForm.mobile,
          email: this.bookingForm.email,
        }
        this.setCustomerDataAddon(data, index)
      }
    },
    addAttendanceCustomer (index) {
      this.attendanceCustomersAddons[index].push({
        opt_marketing: false,
        productCategoryId: null,
        quantity: 1,
        price: 0,
        total_price: 0,
        selectedProduct: { quantity: 1, price: 0 },
        products: new Array(),
        discount: null,
        promotion_code: null,
        additional_fields:[],
      })
      this.attendanceCustomerAdded[index]++
      if (typeof this.attendanceCustomersTotal[index] == 'undefined') {
        this.attendanceCustomersTotal[index] = 0
      }
    },
    grandTotal () {
      var totalPrice = 0
      if (this.bookingForm.products) {
        totalPrice = this.bookingForm.products.reduce(
            (a, b) => a + parseFloat(b.total_price),
            0
        )
      }
      if (this.attendanceCustomers.length) {
        this.attendanceCustomers.map((item) => {
          if (item.products) {
            if (item.products.length) {
              totalPrice += item.products.reduce(
                  (a, b) => a + parseFloat(b.total_price),
                  0
              )
            }
          }
        })
      }
      return totalPrice
    },
    reschedule () {
      if (this.isEnableRepeateBookingReschedule) {
        // let booking = this.repeatDatesForPayments.find((item) => item.isPaid);
        // this.$emit("reschedule", booking.booking_id);
        const bookings = this.repeatDatesForPayments.filter(item => item.isPaid)
        //console.log('bookings')
       // console.log(bookings)
        if (bookings && bookings.length > 1) {
          const bookingIds = bookings.map(item => item.booking_id)
          this.$emit('rescheduleMulti', { booking_ids: bookingIds })
        } else {
          this.$emit('reschedule', bookings[0].booking_id)
        }
      } else {
        this.$emit('reschedule', this.bookingForm.id)
      }
    },
    makeRaceFormat(){
      let data = {
        start_time: this.bookingForm.start_time,
        end_time: this.bookingForm.end_time,
        date: this.bookingForm.date,
        facility_id: this.bookingForm.facility_id,
        venue_service_id: this.bookingForm.venue_service_id,
      };
      this.$emit('addRaceFormat',data);
      this.close();
    },
    addMaintenance () {
      this.maintenanceId = this.facility_id
    },
    closeMaintenance () {
      this.maintenanceId = null
    },
    saveMaintenance () {
      this.$emit('refresh')
      this.close()
    },
    rescheduleFromRepeatBooking (id) {
      this.$emit('reschedule', id)
    },
    close () {
      this.bookingFormTotal = 0
      this.bookingFormAdded = 1
      this.bookingCustomersAddons = []
      this.attendanceCustomersTotal = [0]
      this.attendanceCustomerAdded = [1]
      this.attendanceCustomersAddons = [[]]
      this.$emit('close')
    },
    setCustomerData (data, index = null) {
      //console.log(`setuttit`, data,index)
      if (data.isEmiratesIdCheck) {
        this.isEmiratesIdCheck = true
      }
      if (index === null) {
        this.$set(this.bookingForm, 'additional_fields', []);
        if (data.mobile && data.first_name && data.customer_id) {
          this.isEmiratesIdCheck = false
          if (!this.bookingForm.id) {
            this.bookingForm.promotion_code = null
          }

          this.searchMember(
              data.mobile,
              data.customer_id,
              data.first_name,
              data.last_name
          )
        } else {
          this.clearCardAndBenefits()
        }
        if (!data.customer_id && this.id > 0) {
          this.$set(this.bookingForm, 'customer_id', null)
        }

        if (!data.name && data.first_name) {
          this.$set(this.bookingForm, 'name', data.first_name)
        }
        if (
            this.bookingForm.customer_id &&
            !data.customer_id &&
            this.bookingForm.name != data.name &&
            this.bookingForm.mobile != data.mobile
        )
        {
          this.$set(this.bookingForm, 'mobile', null)
          this.bookingForm.search = null
          this.bookingForm.nameSearch = null
          this.$set(this.bookingForm, 'email', null)
          this.$set(this.bookingForm, 'gender', null)
          this.$set(this.bookingForm, 'name', null)
          this.$set(this.bookingForm, 'customer_id', null)
          this.$set(this.bookingForm, 'first_name', null)
          this.$set(this.bookingForm, 'image_path', null)
          this.$set(this.bookingForm, 'dob', null)
          this.$set(this.bookingForm, 'age_group', null)
          this.$set(this.bookingForm, 'country_id', null)
          this.$set(this.bookingForm, 'last_name', null)
          this.$set(this.bookingForm, 'opt_marketing', false)
          this.$set(this.bookingForm, 'id_proof_type_id', null)
          this.$set(this.bookingForm, 'id_proof_number', null)
          this.$set(this.bookingForm, 'id_proof_path', null)
          this.$set(this.bookingForm, 'customer_tag', null)
          this.$forceUpdate()
        }
        if (data.mobile) this.$set(this.bookingForm, 'mobile', data.mobile)
        if (data.email) this.$set(this.bookingForm, 'email', data.email)
        if (data.country_id) {
          this.$set(this.bookingForm, 'country_id', data.country_id)
        } else {
          if (!this.isEmiratesIdCheck && data.customer_id) {
            this.$set(this.bookingForm, 'country_id', null)
          }
        }
        if (data.gender) {
          this.$set(this.bookingForm, 'gender', data.gender)
        } else {
          if (!this.isEmiratesIdCheck && data.customer_id) {
            this.$set(this.bookingForm, 'gender', null)
          }
        }
        if (data.dob) {
          this.$set(this.bookingForm, 'dob', data.dob)
        } else {
          if (!this.isEmiratesIdCheck && data.customer_id) {
            this.$set(this.bookingForm, 'dob', null)
          }
        }
        if (data.age_group) {
          this.$set(this.bookingForm, 'age_group', data.age_group)
        } else {
          if (!this.isEmiratesIdCheck && data.customer_id) {
            this.$set(this.bookingForm, 'age_group', null)
          }
        }
        if (data.name) {
          data.name = data.name.replace(/\s\s+/g, ' ')
          data.name = data.name.trim()
          this.$set(this.bookingForm, 'name', data.name)
        }
        if (data.last_name) {
          data.last_name = data.last_name.replace(/\s\s+/g, ' ')
          data.last_name = data.last_name.trim()
          this.$set(this.bookingForm, 'last_name', data.last_name)
        } else {
          this.$set(this.bookingForm, 'last_name', null)
        }
        if (data.first_name) {
          data.first_name = data.first_name.replace(/\s\s+/g, ' ')
          data.first_name = data.first_name.trim()
          this.$set(this.bookingForm, 'first_name', data.first_name)
        }
        if (data.customer_id)
          this.$set(this.bookingForm, 'customer_id', data.customer_id)
        if (data.image_path) {
          this.$set(this.bookingForm, 'image_path', data.image_path)
        } else {
          this.$set(this.bookingForm, 'image_path', null)
        }
        if (data.id_proof_type_id) {
          this.$set(
              this.bookingForm,
              'id_proof_type_id',
              data.id_proof_type_id
          )
        } else {
          if (!this.isEmiratesIdCheck && data.customer_id) {
            this.$set(this.bookingForm, 'id_proof_type_id', null)
          }
        }
        if (data.id_proof_number) {
          this.$set(this.bookingForm, 'id_proof_number', data.id_proof_number)
        } else {
          if (!this.isEmiratesIdCheck && data.customer_id) {
            this.$set(this.bookingForm, 'id_proof_number', null)
          }
        }
        if (data.id_proof_path) {
          this.$set(this.bookingForm, 'id_proof_path', data.id_proof_path)
        } else {
          if (!this.isEmiratesIdCheck && data.customer_id) {
            this.$set(this.bookingForm, 'id_proof_path', null)
          }
        }
        if (data.customer_tag) {
          this.$set(this.bookingForm, 'customer_tag', data.customer_tag)
        } else {
          this.$set(this.bookingForm, 'customer_tag', null)
        }

        if (data.id_proof) {
          this.$set(this.bookingForm, 'id_proof', data.id_proof)
        } else {
          if (!this.isEmiratesIdCheck && data.customer_id) {
            this.$set(this.bookingForm, 'id_proof', null)
          }
        }
        if (data.opt_marketing) {
          if (data.opt_marketing == 1) {
            this.$set(this.bookingForm, 'opt_marketing', true)
          } else {
            this.$set(this.bookingForm, 'opt_marketing', false)
          }
        }
        if (data.customer_documents) {
          this.bookingForm.customer_documents = data.customer_documents
          if (
              data.customer_documents[0] &&
              data.customer_documents[0].id_proof_type_id
          )
          {
            this.$set(
                this.bookingForm,
                'id_proof_type_id',
                data.customer_documents[0].id_proof_type_id
            )
          }
          if (
              data.customer_documents[0] &&
              data.customer_documents[0].id_proof_number
          )
          {
            this.$set(
                this.bookingForm,
                'id_proof_number',
                data.customer_documents[0].id_proof_number
            )
          }
          if (
              data.customer_documents[0] &&
              data.customer_documents[0].id_proof_path
          )
          {
            this.$set(
                this.bookingForm,
                'id_proof_path',
                data.customer_documents[0].id_proof_path
            )
          }
        } else {
          if (data.customer_id) {
            this.bookingForm.customer_documents = []
          }
        }

        if(data.additional_data && data.additional_data.length){
          // Use the proper mapping method but exclude disclaimer_form fields
          const mappedFields = this.mapAdditionalDataToFields(data.additional_data);

          // Only update non-disclaimer fields, preserve existing disclaimer signatures
          mappedFields.forEach((field, index) => {
            if (field && field.type !== 'disclaimer_form') {
              this.bookingForm.additional_fields[index] = field;
            }
          });
        }
      } else {
        if (data.mobile && data.first_name && data.customer_id) {
          this.isEmiratesIdCheck = false
          this.searchMember(
              data.mobile,
              data.customer_id,
              data.first_name,
              data.last_name,
              index
          )
        } else {
          this.clearCardAndBenefits(index)
        }

        if (!data.customer_id) {
          this.$set(this.attendanceCustomers[index], 'customer_id', null)
        }

        if (!data.name && data.first_name) {
          this.$set(this.attendanceCustomers[index], 'name', data.first_name)
        }
        if (
            this.attendanceCustomers[index].customer_id &&
            !data.customer_id &&
            this.attendanceCustomers[index].name != data.name &&
            this.attendanceCustomers[index].mobile != data.mobile
        )
        {
          this.$set(this.attendanceCustomers[index], 'mobile', null)
          this.attendanceCustomers[index].search = null
          this.attendanceCustomers[index].nameSearch = null
          this.$set(this.attendanceCustomers[index], 'email', null)
          this.$set(this.attendanceCustomers[index], 'gender', null)
          this.$set(this.attendanceCustomers[index], 'name', null)
          this.$set(this.attendanceCustomers[index], 'customer_id', null)
          this.$set(this.attendanceCustomers[index], 'first_name', null)
          this.$set(this.attendanceCustomers[index], 'image_path', null)
          this.$set(this.attendanceCustomers[index], 'dob', null)
          this.$set(this.attendanceCustomers[index], 'age_group', null)
          this.$set(this.attendanceCustomers[index], 'country_id', null)
          this.$set(this.attendanceCustomers[index], 'last_name', null)
          this.$set(this.attendanceCustomers[index], 'opt_marketing', false)
          this.$set(this.attendanceCustomers[index], 'id_proof_type_id', null)
          this.$set(this.attendanceCustomers[index], 'id_proof_number', null)
          this.$set(this.attendanceCustomers[index], 'id_proof_path', null)
          this.$set(this.attendanceCustomers[index], 'additional_fields', [])


          this.$forceUpdate()
        }
        if (data.mobile)
          this.$set(this.attendanceCustomers[index], 'mobile', data.mobile)
        if (data.email)
          this.$set(this.attendanceCustomers[index], 'email', data.email)
        if (data.country_id) {
          this.$set(
              this.attendanceCustomers[index],
              'country_id',
              data.country_id
          )
        } else {
          if (!this.isEmiratesIdCheck) {
            this.$set(this.attendanceCustomers[index], 'country_id', null)
          }
        }
        if (data.gender) {
          this.$set(this.attendanceCustomers[index], 'gender', data.gender)
        } else {
          if (!this.isEmiratesIdCheck) {
            this.$set(this.attendanceCustomers[index], 'gender', null)
          }
        }
        if (data.dob) {
          this.$set(this.attendanceCustomers[index], 'dob', data.dob)
        } else {
          if (!this.isEmiratesIdCheck) {
            this.$set(this.attendanceCustomers[index], 'dob', null)
          }
        }
        if (data.age_group) {
          this.$set(this.attendanceCustomers[index], 'age_group', data.age_group)
        } else {
          if (!this.isEmiratesIdCheck) {
            this.$set(this.attendanceCustomers[index], 'age_group', null)
          }
        }
        if (data.name) {
          data.name = data.name.replace(/\s\s+/g, ' ')
          data.name = data.name.trim()
          this.$set(this.attendanceCustomers[index], 'name', data.name)
        }
        if (data.last_name) {
          data.last_name = data.last_name.replace(/\s\s+/g, ' ')
          data.last_name = data.last_name.trim()
          this.$set(
              this.attendanceCustomers[index],
              'last_name',
              data.last_name
          )
        } else {
          this.$set(this.attendanceCustomers[index], 'last_name', null)
        }
        if (data.first_name) {
          data.first_name = data.first_name.replace(/\s\s+/g, ' ')
          data.first_name = data.first_name.trim()
          this.$set(
              this.attendanceCustomers[index],
              'first_name',
              data.first_name
          )
        }
        if (data.customer_id)
          this.$set(
              this.attendanceCustomers[index],
              'customer_id',
              data.customer_id
          )
        if (data.image_path) {
          this.$set(
              this.attendanceCustomers[index],
              'image_path',
              data.image_path
          )
        } else {
          this.$set(this.attendanceCustomers[index], 'image_path', null)
        }
        if (data.id_proof_type_id) {
          this.$set(
              this.attendanceCustomers[index],
              'id_proof_type_id',
              data.id_proof_type_id
          )
        } else {
          if (!this.isEmiratesIdCheck) {
            this.$set(
                this.attendanceCustomers[index],
                'id_proof_type_id',
                null
            )
          }
        }
        if (data.id_proof_number) {
          this.$set(
              this.attendanceCustomers[index],
              'id_proof_number',
              data.id_proof_number
          )
        } else {
          if (!this.isEmiratesIdCheck) {
            this.$set(this.attendanceCustomers[index], 'id_proof_number', null)
          }
        }
        if (data.id_proof_path) {
          this.$set(
              this.attendanceCustomers[index],
              'id_proof_path',
              data.id_proof_path
          )
        } else {
          if (!this.isEmiratesIdCheck) {
            this.$set(this.attendanceCustomers[index], 'id_proof_path', null)
          }
        }

        if (data.id_proof) {
          this.$set(this.attendanceCustomers[index], 'id_proof', data.id_proof)
        } else {
          if (!this.isEmiratesIdCheck) {
            this.$set(this.attendanceCustomers[index], 'id_proof', null)
          }
        }
        if (data.opt_marketing) {
          if (data.opt_marketing == 1) {
            this.$set(this.attendanceCustomers[index], 'opt_marketing', true)
          } else {
            this.$set(this.attendanceCustomers[index], 'opt_marketing', false)
          }
        }
        if (data.customer_documents) {
          this.attendanceCustomers[index].customer_documents =
              data.customer_documents
          if (
              data.customer_documents[0] &&
              data.customer_documents[0].id_proof_type_id
          )
          {
            this.$set(
                this.attendanceCustomers[index],
                'id_proof_type_id',
                data.customer_documents[0].id_proof_type_id
            )
          }
          if (
              data.customer_documents[0] &&
              data.customer_documents[0].id_proof_number
          )
          {
            this.$set(
                this.attendanceCustomers[index],
                'id_proof_number',
                data.customer_documents[0].id_proof_number
            )
          }
          if (
              data.customer_documents[0] &&
              data.customer_documents[0].id_proof_path
          )
          {
            this.$set(
                this.attendanceCustomers[index],
                'id_proof_path',
                data.customer_documents[0].id_proof_path
            )
          }
        } else {
          this.attendanceCustomers[index].customer_documents = []
        }
        if (data.customer_tag) {
          this.$set(
              this.attendanceCustomers[index],
              'customer_tag',
              data.customer_tag
          )
        } else {
          this.$set(this.attendanceCustomers[index], 'customer_tag', null)
        }
        if (data.additional_data && data.additional_data.length) {
          this.$set(this.attendanceCustomers[index], 'additional_fields', data.additional_data);
        }
      }
      this.$refs.form.resetValidation()
      this.$forceUpdate()
    },
    setMemberData (data, index = null) {
      this.setCustomerData(data, index)
      if (index === null) {
        this.$set(this.bookingForm, 'card_number', data.card_number)
        this.$set(this.bookingForm, 'membership_id', data.membership_id)
      } else {
        this.$set(
            this.attendanceCustomers[index],
            'card_number',
            data.card_number
        )
        this.$set(
            this.attendanceCustomers[index],
            'membership_id',
            data.membership_id
        )

      }

      //this.verifyBenefit("membership", index);
    },
    confirmAddOrEditBooking () {
      if (this.id && this.perCapacity == 1) {
        this.confirmModel = {
          id: this.order_id,
          title: `Do you want update this booking?`,
          description: `This will update the reservation. By clicking <b>Yes</b> you can confirm update operation`,
          type: 'update',
          data: {
            id: this.id,
            notify_customers: false
          }
        }
      } else {
        this.addOrEditBooking()
      }
    },
    addOrEditBooking () {
      this.confirmModel.id = null
      if (this.perCapacity == 1) {
        let count =
            this.bookingFormTotal +
            this.attendanceCustomersTotal.reduce(
                (partialSum, a) => partialSum + a,
                0
            )
        if (this.capacity < count) {
          this.showError('Trying to book more than available capacity')
          return
        }
        // console.log("this.bookingFormTotal");
        // console.log(this.bookingFormTotal);
        // console.log("this.attendanceCustomersTotal");
        // console.log(this.attendanceCustomersTotal);
        // console.log("count");
        // console.log(count);
        // console.log("this.capacity");
        // console.log(this.capacity);
        // return;
      }

      //console.log(`-------`, this.attendanceCustomers)
      this.attendanceData = this.attendanceCustomers
      if (this.attendanceData.length > 0) {
        let mobiles = this.attendanceData.map(function (item) {
          return item.mobile
        })
        if (this.bookingForm.mobile) {
          mobiles.push(this.bookingForm.mobile)
        }
      }
      if (!this.$refs.form.validate()) {
        this.showError('Please fill all required fields to continue')
        return
      }

      if (this.repeatId && this.bookingForm.id > 0) {
        this.bookingForm.products = this.currentOrderProducts
      } else {
        var repeatBookingAvailableDates = []
        if (this.bookingForm.repeat) {
          this.bookingForm.repeats.forEach((repeats) => {
            repeatBookingAvailableDates = repeatBookingAvailableDates.concat(repeats.available_dates)
          })
          repeatBookingAvailableDates = repeatBookingAvailableDates.sort(
              (a, b) => new Date(a) - new Date(b)
          )
        }
      }

      if (!this.bookingForm.repeats && !this.id) {
        this.bookingForm.repeat = false
        delete this.bookingForm.repeats
      }
      if (this.bookingForm.products.length == 0) {
        this.showError('Please add atleast one product')
        return
      }
      this.showLoader()
      //console.log(`bookingForm------`,this.bookingForm)
      var formData = new FormData()
      for (let key in this.bookingForm) {
        if (
            this.bookingForm[key] != null &&
            !Array.isArray(this.bookingForm[key]) &&
            typeof this.bookingForm[key] != 'object'
        )
        {
          formData.append(`${key}`, this.bookingForm[key])
        } else if (Array.isArray(this.bookingForm[key])) {
          this.bookingForm[key].forEach((data, index) => {
            if (!Array.isArray(data)) {
              for (let innerKey in data) {
                if (Array.isArray(data[innerKey])) {
                  if (
                      innerKey != 'start_times' &&
                      innerKey != 'end_times' &&
                      innerKey != 'times'
                  )
                  {
                    data[innerKey].forEach((deepData, deepIndex) => {
                      formData.append(
                          `${key}[${index}][${innerKey}][${deepIndex}]`,
                          typeof deepData == 'object'
                              ? JSON.stringify(deepData)
                              : deepData
                      )
                    })
                  }
                } else {
                  formData.append(
                      `${key}[${index}][${innerKey}]`,
                      data[innerKey]
                  )
                }
              }
            } else if (Array.isArray(data)) {
              data.forEach((innerData, innerIndex) => {
                formData.append(`${key}[${index}][${innerIndex}]`, innerData)
              })
            } else {
              formData.append(
                  `${key}[${index}]`,
                  typeof data == 'object' ? JSON.stringify(data) : data
              )
            }
          })
        }
      }

      if (this.perCapacity === 1 && this.bookingCustomersAddons.length > 0) {
        for (let main in this.bookingCustomersAddons) {
          for (let keyInner in this.bookingCustomersAddons[main]) {
            if (
                this.bookingCustomersAddons[main][keyInner] != null &&
                !Array.isArray(this.bookingCustomersAddons[main][keyInner]) &&
                typeof this.bookingCustomersAddons[main][keyInner] != 'object'
            )
            {
              formData.append(
                  `addOnCustomers[${main}][${keyInner}]`,
                  this.bookingCustomersAddons[main][keyInner]
              )
            } else if (
                Array.isArray(this.bookingCustomersAddons[main][keyInner])
            )
            {
              this.bookingCustomersAddons[main][keyInner].forEach(
                  (data, index) => {
                    // console.log(data);
                    if (!Array.isArray(data)) {
                      for (let innerKey in data) {
                        if (Array.isArray(data[innerKey])) {
                          if (
                              innerKey != 'start_times' &&
                              innerKey != 'end_times' &&
                              innerKey != 'times'
                          )
                          {
                            data[innerKey].forEach((deepData, deepIndex) => {
                              formData.append(
                                  `addOnCustomers[${main}][${keyInner}][${index}][${innerKey}][${deepIndex}]`,
                                  typeof deepData == 'object'
                                      ? JSON.stringify(deepData)
                                      : deepData
                              )
                            })
                          }
                        } else {
                          formData.append(
                              `addOnCustomers[${main}][${keyInner}][${index}][${innerKey}]`,
                              data[innerKey]
                          )
                        }
                      }
                    } else if (Array.isArray(data)) {
                      data.forEach((innerData, innerIndex) => {
                        formData.append(
                            `addOnCustomers[${main}][${keyInner}][${index}][${innerIndex}]`,
                            innerData
                        )
                      })
                    } else {
                      formData.append(
                          `addOnCustomers[${main}][${keyInner}][${index}]`,
                          typeof data == 'object' ? JSON.stringify(data) : data
                      )
                    }
                  }
              )
            }
          }
        }
      }

      //console.log(`attendanceData`,this.attendanceData)
      if (
          this.attendanceData.length > 0 &&
          !this.bookingForm.id &&
          this.perCapacity === 1
      )
      {
        for (let key in this.attendanceData) {
          if (
              typeof this.attendanceData[key] === 'object' &&
              this.attendanceData[key].mobile
          )
          {
            if (this.attendanceData[key].products) {
              if (this.attendanceData[key].products.length == 0) {
                this.hideLoader()
                this.showError('Please add atleast one product')
                return
              }
            } else {
              this.showError('Please add atleast one product')
              return
            }
            formData.append(
                `attendance_customer[${key}][venue_service_id]`,
                this.venue_service_id
            )
            formData.append(
                `attendance_customer[${key}][first_name]`,
                this.attendanceData[key].first_name
            )
            formData.append(
                `attendance_customer[${key}][last_name]`,
                this.attendanceData[key].last_name
            )
            formData.append(
                `attendance_customer[${key}][mobile]`,
                this.attendanceData[key].mobile
            )
            formData.append(
                `attendance_customer[${key}][email]`,
                this.attendanceData[key].email
            )
            if (
                this.attendanceData[key].customer_id &&
                this.attendanceData[key].customer_id != null
            )
            {
              formData.append(
                  `attendance_customer[${key}][customer_id]`,
                  this.attendanceData[key].customer_id
              )
            }
            if (this.attendanceData[key].name) {
              formData.append(
                  `attendance_customer[${key}][name]`,
                  this.attendanceData[key].name
              )
            } else {
              formData.append(
                  `attendance_customer[${key}][name]`,
                  this.attendanceData[key].namesearch.first_name
              )
            }
            if (this.attendanceData[key].promotion_code) {
              formData.append(
                  `attendance_customer[${key}][promotion_code]`,
                  this.attendanceData[key].promotion_code
              )
            }
            if (this.attendanceData[key].card_number) {
              formData.append(
                  `attendance_customer[${key}][card_number]`,
                  this.attendanceData[key].card_number
              )
            }
            if (this.attendanceData[key].customer_type) {
              formData.append(
                  `attendance_customer[${key}][customer_type]`,
                  this.attendanceData[key].customer_type
              )
            }
            if (this.attendanceData[key].opt_marketing) {
              formData.append(
                  `attendance_customer[${key}][opt_marketing]`,
                  this.attendanceData[key].opt_marketing
              )
            }
            if (this.attendanceData[key].discount) {
              for (let dKey in this.attendanceData[key].discount) {
                formData.append(
                    `attendance_customer[${key}][discount][${dKey}]`,
                    this.attendanceData[key].discount[dKey]
                )
              }
            }
            /** optional field data */
            if (this.attendanceData[key].gender) {
              formData.append(
                  `attendance_customer[${key}][gender]`,
                  this.attendanceData[key].gender
              )
            }
            if (this.attendanceData[key].dob) {
              formData.append(
                  `attendance_customer[${key}][dob]`,
                  this.attendanceData[key].dob
              )
            }
            if (this.attendanceData[key].age_group) {
              formData.append(
                  `attendance_customer[${key}][age_group]`,
                  this.attendanceData[key].age_group
              )
            }
            if (this.attendanceData[key].country_id) {
              formData.append(
                  `attendance_customer[${key}][country_id]`,
                  this.attendanceData[key].country_id
              )
            }
            if (this.attendanceData[key].id_proof_type_id) {
              formData.append(
                  `attendance_customer[${key}][id_proof_type_id]`,
                  this.attendanceData[key].id_proof_type_id
              )
            }
            if (this.attendanceData[key].id_proof_number) {
              formData.append(
                  `attendance_customer[${key}][id_proof_number]`,
                  this.attendanceData[key].id_proof_number
              )
            }
            if (this.attendanceData[key].id_proof) {
              formData.append(
                  `attendance_customer[${key}][id_proof]`,
                  this.attendanceData[key].id_proof
              )
            }
            if (this.attendanceData[key].profile_image) {
              formData.append(
                  `attendance_customer[${key}][profile_image]`,
                  this.attendanceData[key].profile_image
              )
            }
            if (this.attendanceData[key].image_path) {
              formData.append(
                  `attendance_customer[${key}][image_path]`,
                  this.attendanceData[key].image_path
              )
            }
            formData.append(
                `attendance_customer[${key}][price]`,
                this.attendanceData[key].price
            )
            formData.append(
                `attendance_customer[${key}][total_price]`,
                this.attendanceData[key].total_price
            )

            this.attendanceData[key].additional_fields.forEach((field, index) => {
              Object.keys(field).forEach(ke => {
                const value = field[ke];
                const baseKey = `attendance_customer[${key}][additional_fields][${index}][${ke}]`;

                if (Array.isArray(value)) {
                  value.forEach((item, i) => {
                    formData.append(`${baseKey}[${i}]`, item);
                  });
                } else {
                  formData.append(baseKey, value);
                }
              });
            });

            // console.log(this.attendanceCustomersAddons[key]);

            if (
                this.attendanceCustomersAddons[key].length > 0 &&
                this.perCapacity === 1
            )
            {
              for (let main in this.attendanceCustomersAddons[key]) {
                for (let keyInner in this.attendanceCustomersAddons[key][
                    main
                    ])
                {
                  if (
                      this.attendanceCustomersAddons[key][main][keyInner] !=
                      null &&
                      !Array.isArray(
                          this.attendanceCustomersAddons[key][main][keyInner]
                      ) &&
                      typeof this.attendanceCustomersAddons[key][main][
                          keyInner
                          ] != 'object'
                  )
                  {
                    formData.append(
                        `attendance_customer[${key}][addOnCustomers][${main}][${keyInner}]`,
                        this.attendanceCustomersAddons[key][main][keyInner]
                    )
                  } else if (
                      Array.isArray(
                          this.attendanceCustomersAddons[key][main][keyInner]
                      )
                  )
                  {
                    this.attendanceCustomersAddons[key][main][keyInner].forEach(
                        (data, index) => {
                          // console.log(data);
                          if (!Array.isArray(data)) {
                            for (let innerKey in data) {
                              if (Array.isArray(data[innerKey])) {
                                if (
                                    innerKey != 'start_times' &&
                                    innerKey != 'end_times' &&
                                    innerKey != 'times'
                                )
                                {
                                  data[innerKey].forEach(
                                      (deepData, deepIndex) => {
                                        formData.append(
                                            `attendance_customer[${key}][addOnCustomers][${main}][${keyInner}][${index}][${innerKey}][${deepIndex}]`,
                                            typeof deepData == 'object'
                                                ? JSON.stringify(deepData)
                                                : deepData
                                        )
                                      }
                                  )
                                }
                              } else {
                                formData.append(
                                    `attendance_customer[${key}][addOnCustomers][${main}][${keyInner}][${index}][${innerKey}]`,
                                    data[innerKey]
                                )
                              }
                            }
                          } else if (Array.isArray(data)) {
                            data.forEach((innerData, innerIndex) => {
                              formData.append(
                                  `attendance_customer[${key}][addOnCustomers][${main}][${keyInner}][${index}][${innerIndex}]`,
                                  innerData
                              )
                            })
                          } else {
                            formData.append(
                                `attendance_customer[${key}][addOnCustomers][${main}][${keyInner}][${index}]`,
                                typeof data == 'object'
                                    ? JSON.stringify(data)
                                    : data
                            )
                          }
                        }
                    )
                  }
                }
              }
            }

            this.attendanceData[key].products.map((item, index) => {
              for (var iKey in item) {
                if (iKey != 'discount') {
                  formData.append(
                      `attendance_customer[${key}][products][${index}][${iKey}]`,
                      item[iKey]
                  )
                } else {
                  for (let dKey in item[iKey]) {
                    formData.append(
                        `attendance_customer[${key}][products][${index}][${iKey}][${dKey}]`,
                        item[iKey][dKey]
                    )
                  }
                }
              }
            })
          }
        }
      }

      if (this.bookingForm.id_proof) {
        formData.append('id_proof', this.bookingForm.id_proof)
      }

      if (this.bookingForm.profile_image) {
        formData.append('profile_image', this.bookingForm.profile_image)
      }

      // Add main customer's additional fields to FormData
      console.log('🚀 MAIN CUSTOMER additional_fields:', this.bookingForm.additional_fields);
      if (this.bookingForm.additional_fields && this.bookingForm.additional_fields.length > 0) {
        this.bookingForm.additional_fields.forEach((field, index) => {
          console.log(`🚀 Processing main customer field ${index}:`, field);
          for (const key in field) {
            const value = field[key];
            const baseKey = `additional_fields[${index}][${key}]`;

            if (Array.isArray(value)) {
              value.forEach((item, i) => {
                formData.append(`${baseKey}[${i}]`, item);
              });
            } else {
              formData.append(baseKey, value);
              console.log(`🚀 Appended main customer: ${baseKey} =`, value instanceof File ? '(File)' : value);
            }
          }
        });
      }

      if (this.id && this.perCapacity === 1) {
        formData.append('notify_customers', this.notify_customers)
      }
      if (
          this.bookingForm.repeat &&
          repeatBookingAvailableDates &&
          repeatBookingAvailableDates.length > 0
      )
      {
        formData.delete('date')
        formData.delete('start_time')
        formData.delete('end_time')
        formData.append('start_time', this.bookingForm.repeats[this.bookingForm.repeats.length - 1].start_time)
        formData.append('end_time', this.bookingForm.repeats[this.bookingForm.repeats.length - 1].end_time)
        formData.append('date', repeatBookingAvailableDates[repeatBookingAvailableDates.length - 1])
      }
      if (this.bookingForm.id) {
        formData.set('attendance_count', 1)
      }

      if (this.bookingForm.sales_team_id) {
        formData.append('sales_team_id', this.bookingForm.sales_team_id)
      }
      if (this.bookingForm.assigned_trainers && this.bookingForm.assigned_trainers.length > 0){
        this.bookingForm.assigned_trainers.forEach( (tId,key) => {
          formData.append(`assigned_trainer[${key}]`, tId);
        })
      }
      formData.append('deleted_products', JSON.stringify(this.deletedProducts))
      if (this.enableOvernightBooking) {
        formData.append('enable_overnight_booking', 1)
      } else {
        formData.append('enable_overnight_booking', 0)
      }
      this.$http
          .post(
              `venues/facilities/bookings${
                  this.bookingForm.id > 0 ? '/' + this.bookingForm.id : ''
              }`,
              formData,
              {
                headers: {
                  'Content-Type': 'multipart/form-data; boundary=${form._boundary}',
                },
              }
          )
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              let data = response.data.data
              this.hideLoader()
              if (this.repeatId) {
                this.$emit('booked')
              } else {
                this.$emit('booked', data.order_id)
              }
              this.attendanceCustomersTotal = [0]
              this.attendanceCustomerAdded = [1]
              this.attendanceCustomersAddons = [[]]
              this.bookingFormTotal = 0
              this.bookingFormAdded = 1
              this.bookingCustomersAddons = []
              this.notify_customers = false
              this.showSuccess('Booking updated successfully')
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          })
    },
    getFacilityUtils () {
      this.showLoader('Loading')
      this.calculateHeight();
      this.endTimes = []
      this.$http
          .get(
              `venues/facilities/bookings/utils?facility_id=${this.facility_id}&date=${this.date}&start_time=${this.start_time}&increment=${this.increment}&min_booking_time=${this.minBookingTime}&booking_id=${this.id}&per_capacity=${this.perCapacity}&venue_service_id=${this.venue_service_id}&enable_overnight_booking=${this.enableOvernightBooking}`
          )
          .then((response) => {
            this.hideLoader()
            if (response.status == 200 && response.data.status == true) {
              let data = response.data.data
              this.bookingWithOpenProduct = false
              this.capacity = 0
              if (data.is_enable_per_day_capacity && data.capacity && data.total_attendance) {
                const remainingCapacity = data.capacity - data.total_attendance
                this.capacity = remainingCapacity >= 1 ? remainingCapacity : 0
              } else {
                this.capacity = data.capacity
              }

              this.productCombinations = []
              this.endTimes = data.end_times
              if (this.enableOvernightBooking) {
                this.addOverNightBookingTimes(data)
              }
              this.facilityStartTime = data.opening_time
              this.facilityEndTime = data.closing_time
              this.bookingForm.start_time = data.start_time
              this.bookingForm.end_time = data.end_time
              let start_time = moment(this.start_time, 'HH:mm:ss').format('HH:mm:ss')
              let currentTime = moment(new Date()).format('HH:mm:ss')

              if (moment(start_time, 'HH:mm:ss').isBefore(moment(currentTime, 'HH:mm:ss'))) {
                this.pastTime = true
              } else {
                this.pastTime = false
              }
              this.categories = data.categories
              this.categoriesList = [
                { name: 'All', id: null },
                { name: 'Open Product', id: -1 },
                ...this.categories,
              ]
              if (data.facility_rentals.length > 0 && this.id == 0 && this.perCapacity == 0) {
                let rentalProducts = data.default_products
                this.bookingForm.price = 0
                this.bookingForm.total_price = 0
                this.bookingForm.products = []
                rentalProducts.forEach((rentalProduct) => {
                  this.bookingForm.price += Number(rentalProduct.price)
                  this.bookingForm.total_price += Number(rentalProduct.price) +
                      Number(rentalProduct.quantity >= 1
                          ? rentalProduct.total_tax_amount ? rentalProduct.total_tax_amount : rentalProduct.tax_amount *
                              parseFloat(rentalProduct.quantity)
                          : rentalProduct.tax_amount)

                  this.bookingForm.products.push({
                    product_id: rentalProduct.id,
                    product_type_id: rentalProduct.product_type_id,
                    price: rentalProduct.price,
                    name: rentalProduct.name,
                    tax: rentalProduct.tax_amount,
                    total_tax_amount: rentalProduct.total_tax_amount,
                    seasonal_pricing_id: rentalProduct.seasonal_pricing_id,
                    category_id: rentalProduct.category_id,
                    rental: true,
                    product_price: Number(rentalProduct.product_price_when_overlapping
                        ? rentalProduct.product_price_when_overlapping
                        : rentalProduct.product_price),
                    quantity: rentalProduct.quantity,
                    total_price:
                        Number(rentalProduct.price) +
                        Number(rentalProduct.quantity >= 1
                            ?
                            rentalProduct.total_tax_amount ? rentalProduct.total_tax_amount :
                                rentalProduct.tax_amount *
                                parseFloat(rentalProduct.quantity)
                            : rentalProduct.tax_amount),
                  })
                })
              } else {
                if (this.perCapacity == 1) {
                  this.$http
                      .get(
                          `venues/facilities/bookings/participants?facility_id=${this.facility_id}&start_time=${this.bookingForm.start_time}&end_time=${this.bookingForm.end_time}&date=${this.date}`
                      )
                      .then((participans_response) => {
                        this.hideLoader()
                        if (
                            participans_response.status == 200 &&
                            participans_response.data.status == true
                        )
                        {
                          const data_participans_response = participans_response.data.data
                          this.totalParticipants = data_participans_response.reduce((a, b) => a + parseFloat(b.attendance), 0)
                          this.bookedCapacity += this.totalParticipants
                          if (this.bookedCapacity >= this.capacity) {
                            this.attendanceSwitch = false
                          } else {
                            this.attendanceSwitch = true
                          }
                          this.editFlag = false
                        }
                      })
                  this.bookingForm.products = []
                  this.bookingForm.price = 0
                  this.bookingForm.total_price = 0
                }
              }
              if (this.perCapacity == 1) {
                if (data.facility_rentals[0]) {
                  this.categoriesList.push({
                    name: 'Tickets',
                    id: data.facility_rentals[0].category_id,
                  })
                }
              }
              if (data.facility_rentals.length) {
                if (data.facility_rentals[0]) {
                  let rentalProducts = {
                    id: data.facility_rentals[0].category_id,
                    name: this.perCapacity == 1 ? 'Tickets' : 'Rentals',
                    products: [
                      ...data.facility_rentals.map((item) => {
                        item.id = item.product_id
                        item.rental = true
                        return item
                      }),
                    ],
                  }
                  this.categories.push(rentalProducts)
                }
              }
              this.bookingForm.opt_marketing = false
              this.$refs.form.resetValidation()
              if (this.id > 0) {
                this.getBookingDetails()
              }
              this.hideLoader()
              this.$forceUpdate()
            }
          })
          .catch((error) => {
            this.close()
            this.$emit('refresh')
            this.errorChecker(error)
          })
    },
    getBookingDetails () {
      this.showLoader('Loading Facilities')
      this.facilities = []
      this.$http
          .get(`venues/facilities/bookings/${this.bookingForm.order_id}`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.isEmiratesIdCheck = false
              this.disablePromotion = true
              const data = response.data.data
              this.uploadedAttachments = data.facility_booking.length > 0 && data.facility_booking[0] ? data.facility_booking[0].documents : []
              this.bookingForm.order_notes = data.order_notes
              if (data.facility_booking.length > 0) {
                let bookingData = data.facility_booking.find(
                    (x) =>
                        x.date === this.date &&
                        x.start_time == this.start_time &&
                        x.facility_booking_duplicate_id == null
                )
                if (!bookingData) {
                  bookingData = data.facility_booking.find(
                      (x) =>
                          x.date === this.date &&
                          x.facility_booking_duplicate_id == null
                  )
                }
                // check is overnight
                if (!bookingData) {
                  bookingData = data.facility_booking.find((x) =>
                      x.booking_end_date === this.date &&
                      x.id == this.id &&
                      x.facility_booking_duplicate_id == null
                  )
                  if (bookingData) {
                    this.date = bookingData.date
                    this.start_time = bookingData.start_time
                    this.end_time = bookingData.end_time
                    this.bookingForm.date = bookingData.date
                    this.bookingForm.start_time = bookingData.start_time
                    this.bookingForm.end_time = bookingData.end_time
                  }
                }

                this.bookingForm.id = bookingData.id;
                this.bookingForm.customer_type = bookingData.customer_type;
                this.bookingForm.id_proof_path = bookingData.id_proof;
                this.bookingForm.id_proof_number = bookingData.id_proof_number;
                this.bookingForm.id_proof_type_id = bookingData.id_proof_type_id;
                this.bookingForm.image_path = bookingData.image_path;
                this.bookingForm.start_time = bookingData.start_time;
                this.bookingForm.end_time = bookingData.end_time;
                this.bookingForm.attendance_count = bookingData.attendance;
                this.bookingForm.is_booking_approved = bookingData.is_booking_approved;
                let selectedTrainers = [];
                if(bookingData.trainer_assignment){
                  bookingData.trainer_assignment.forEach( (t) => {
                    selectedTrainers.push(t.trainer_id)
                  });
                }
                this.bookingForm.assigned_trainers = [...selectedTrainers];
                //console.log("this.bookingForm.assigned_trainers",this.bookingForm.assigned_trainers);
                //this.bookingForm.assigned_trainers = bookingData.trainer_assignment ? bookingData.trainer_assignment.trainer_id : null;
                this.attendies = bookingData.attendance;
              }

              if (data.discount != null) {
                if (data.discount.promotion != null) {
                  this.bookingForm.promotion_code =
                      data.discount.promotion.promotion_code
                }
                if (data.discount.member != null) {
                  this.bookingForm.customer_type = 'member'
                  this.bookingForm.card_number =
                      data.discount.member.display_number
                }
                this.bookingForm.discount = {
                  actual_price: data.discount.actual_price,
                  actual_tax: data.discount.actual_tax,
                  actual_total: data.discount.actual_total,
                }
              }

              if (data.parent_order_id && data.parent_orders_id.length > 0) {
                this.bookingForm.parent_order_id = data.parent_order_id
                this.bookingForm.parent_orders_id = data.parent_orders_id
              } else {
                this.bookingForm.parent_orders_id = null
              }

              if (data.customer) {
                this.bookingForm.name = `${data.customer.first_name}${
                    data.customer.last_name ? ' ' + data.customer.last_name : ''
                }`
                this.bookingForm.first_name = data.customer.first_name
                this.bookingForm.last_name = data.customer.last_name
                this.bookingForm.mobile = data.customer.mobile
                this.bookingForm.email = data.customer.email
                this.bookingForm.gender = data.customer.gender
                this.bookingForm.dob = data.customer.dob
                this.bookingForm.age_group = data.customer.age_group
                this.bookingForm.country_id = data.customer.country_id
                if (data.customer.venue_customer_tags) {
                  const tags = []
                  data.customer.venue_customer_tags.map((cvt) => {
                    tags.push({ id: cvt.tag.id, name: cvt.tag.name })
                  })
                  this.bookingForm.customer_tag = tags
                }
                this.bookingForm.opt_marketing = data.customer.opt_marketing == 1 ? true : false

                if (data.customer.customer_documents) {
                  this.bookingForm.customer_documents = data.customer.customer_documents
                }

                if (this.bookingForm.customer_type == 'member' && !this.bookingForm.card_number) {
                  this.searchMember(
                      data.customer.mobile,
                      data.customer.customer_id,
                      data.customer.first_name,
                      data.customer.last_name
                  )
                }
                if(data.customer.additional_data && data.customer.additional_data.length){
                  this.bookingForm.additional_fields = this.mapAdditionalDataToFields(data.customer.additional_data);
                } else {
                  this.bookingForm.additional_fields = this.getDefaultAdditionalFields();
                }
              }
              this.bookingFormAdded = 1
              this.bookingFormTotal = 1
              let fbId = data.facility_booking && data.facility_booking.length > 0 ? data.facility_booking[0].id : null
              let group_customers = data.group_customers
              if (data.group_customers.length > 0) {
                this.current_group_customer = data.group_customers[0]
              }
              group_customers.shift()

              let test = []
              Object.values(group_customers).forEach((data1) => {
                if (!fbId || !data1.gc_fb_id || fbId == data1.gc_fb_id) {
                  const tags = []
                  if (
                      data1.venue_customer_tags &&
                      data1.venue_customer_tags.length
                  )
                  {
                    data1.venue_customer_tags.map((cvt) => {
                      tags.push({ id: cvt.tag.id, name: cvt.tag.name })
                    })
                  }
                  test.push({
                    attendance: false,
                    attendance_count: 1,
                    total_price: 0,
                    price: 0,
                    discount: null,
                    promotion_code: null,
                    customer_id: data1.customer_id,
                    name: `${data1.first_name}${
                        data1.last_name ? ' ' + data1.last_name : ''
                    }`,
                    first_name: data1.first_name,
                    last_name: data1.last_name,
                    mobile: data1.mobile,
                    email: data1.email,
                    gender: data1.gender,
                    dob: data1.dob,
                    age_group: data1.age_group,
                    country_id: data1.country_id,
                    opt_marketing: data1.opt_marketing == 1 ? true : false,
                    customer_tag: tags,
                    additional_fields: data1.additional_data && data1.additional_data.length
                      ? this.mapAdditionalDataToFields(data1.additional_data)
                      : this.getDefaultAdditionalFields(),
                  })
                }
                // this.bookingFormAdded++;
                this.bookingFormTotal++
              })

              this.bookingCustomersAddons = test
              this.bookingFormAdded =
                  this.bookingFormAdded +
                  (this.bookingCustomersAddons.length
                      ? this.bookingCustomersAddons.length
                      : 0)

              if (data.group_customers.length) {
                this.$http
                    .get(
                        `venues/facilities/bookings/participants?facility_id=${this.facility_id}&start_time=${this.bookingForm.start_time}&end_time=${this.bookingForm.end_time}&date=${this.date}`
                    )
                    .then((participans_response) => {
                      if (
                          participans_response.status == 200 &&
                          participans_response.data.status == true
                      )
                      {
                        const data_participans_response = Array.from(
                            new Set(participans_response.data.data.map((a) => a.id))
                        ).map((id) => {
                          return participans_response.data.data.find(
                              (a) => a.id === id
                          )
                        })

                        this.bookedCapacity = data_participans_response.reduce(
                            (a, b) => a + parseFloat(b.attendance),
                            0
                        )
                      }
                    })
                this.attendanceCustomers = data.group_customers

                // this.attendies = this.attendanceCustomers.length;

                this.attendanceCustomers.shift()

                if (this.attendanceCustomers.length == 0) {
                  this.bookingForm.attendance = true
                  if (this.bookedCapacity < this.capacity) {
                    this.attendanceSwitch = true
                  } else {
                    this.attendanceSwitch = false
                  }
                } else {
                  this.bookingForm.attendance = true
                  this.attendanceSwitch = true
                }
                this.editFlag = true
              }
              this.bookingForm.price = data.price
              this.bookingForm.status_id = data.status_id
              this.bookingForm.order_id = data.id
              this.bookingForm.customer_id = data.customer_id

              if (data.company_sale != null) {
                this.bookingForm.company_id = data.company_sale.company_id
                this.bookingForm.company_sale_id = data.company_sale.id
                this.getActiveCompanySales()
              }

              this.bookingForm.products = []
              this.currentOrderProducts = []
              const quantityMap = data.items.reduce((map, item) => {
                const id = item.product_id;
                const qty = Number(item.quantity) || 0;
                map[id] = (map[id] || 0) + qty;
                return map;
              }, {});

              this.categories.forEach(category => {
                if (!category.products) return;
                category.products.forEach(product => {
                  const pid = product.product_id;
                  if (quantityMap[pid] !== undefined && product.remaining_capacity !== undefined && product.enable_inventory == 1) {
                    product.remaining_capacity += quantityMap[pid];
                  }
                });
              });
              data.items.forEach((product) => {
                let pdata = {
                  order_item_id: product.order_item_id,
                  product_id: product.product_id,
                  inventory_enable: product.inventory_enable,
                  product_type_id: product.product_type_id,
                  category_id: product.category_id,
                  quantity: product.quantity,
                  seasonal_pricing_id: product.seasonal_pricing_id,
                  price: Number(product.price),
                  rental: product.rental != null ? true : false,
                  is_full_day:
                      product.rental != null ? product.rental.is_full_day : false,
                  venue_service_id: this.venue_service_id,
                  discount: product.discount ? product.discount : false,
                  name: product.name ? product.name : 'Product Name',
                  product_price: Number(product.product_price),
                  tax: Number(product.tax),
                  participant_count: product.participant_count,
                  total_price: Number(product.total),
                  initial_quantity: product.quantity,
                }
                if (product.category_name == 'open_product' && product.rental) {
                  this.bookingWithOpenProduct = true
                }
                this.currentOrderProducts.push(pdata)
                this.bookingForm.products.push(pdata)
              })
              this.bookingForm.total_price = this.bookingForm.products.reduce(
                  (a, b) => a + parseFloat(b.total_price),
                  0
              )
              if (
                  data.facility_booking.length > 0 &&
                  data.facility_booking[0].repeat
              )
              {
                let repeatData = data.facility_booking[0].repeat
                this.bookingForm.repeat_dates_for_payment = []
                this.bookingForm.repeat = true
                this.bookingForm.repeats = repeatData.meta
                this.bookingForm.repeat_id = repeatData.id
                data.facility_booking.forEach((data) => {
                  this.bookingForm.repeat_dates_for_payment.push({
                    date_id: data.id,
                    date: data.date,
                  })
                })
                setTimeout(() => {
                  this.repeatId = repeatData.id
                })

                this.disablePromotion = true
                this.$forceUpdate()
              }
              this.customerFormRefresh = now()
              this.updateBookingFormQuantity()
              this.checkFullDayProduct()
              this.hideLoader()
              this.$forceUpdate()
            }
          })
          .catch((error) => {
            this.hideLoader()
            this.errorChecker(error)
          })
    },
    removeProduct (pIndex, index = null) {
      if (index === null) {
        // console.log("hi");
        let data = this.bookingForm.products[pIndex]
        if (this.bookingForm.products[pIndex] && this.bookingForm.products[pIndex].inventory_enable) {
          if (data.order_item_id) {
            this.deletedProducts.push(data.order_item_id)
          }
          let products = this.categories.find(
              (item) => item.id == data.category_id
          ).products
          if (products) {
            products.forEach((el) => {
              if (el.id == data.product_id) {
                el.sales -= data.quantity
              }
            })
          }
        }
        this.bookingForm.products.splice(pIndex, 1)
        if (this.bookingForm.repeats && this.bookingForm.repeats.length > 0) {
          this.bookingForm.repeats.forEach((repeat) => {
            let findIndex = repeat.products.findIndex(
                (x) => x.id == data.product_id
            )
            if (findIndex != -1) {
              repeat.products.splice(pIndex, 1)
            }
          })
        }

        if (data.rental == false && this.repeatId) {
          let findIndex = this.currentOrderProducts.findIndex(
              (x) => x.product_id == data.product_id
          )
          if (findIndex != null) {
            this.currentOrderProducts.splice(findIndex, 1)
          }
        }
        this.$forceUpdate()
        this.bookingForm.total_price = this.bookingForm.products.reduce(
            (a, b) => a + parseFloat(b.total_price),
            0
        )
        if (this.bookingForm.promotion_code != null) {
          this.verifyBenefit('promotion')
        }
        if (this.bookingForm.card_number != null) {
          this.verifyBenefit('membership')
        }
        this.refreshComponent++
        this.updateBookingFormQuantity()
        this.refreshCustomersMain()
        this.$forceUpdate()
      } else {
        let data = this.attendanceCustomers[index].products[pIndex]
        if (this.attendanceCustomers[index].products[pIndex].inventory_enable) {
          if (data.order_item_id) {
            this.deletedProducts.push(data.order_item_id)
          }
          let products = this.categories.find(
              (item) => item.id == data.category_id
          ).products
          if (products) {
            products.forEach((el) => {
              if (el.id == data.product_id) {
                el.sales -= data.quantity
              }
            })
          }
        }
        this.attendanceCustomers[index].products.splice(pIndex, 1)
        if (this.bookingForm.repeats && this.bookingForm.repeats.length > 0) {
          this.bookingForm.repeats.forEach((repeat) => {
            let findIndex = repeat.products.findIndex(
                (x) => x.id == data.product_id
            )
            if (findIndex != -1) {
              repeat.products.splice(pIndex, 1)
            }
          })
        }

        if (data.rental == false && this.repeatId) {
          let findIndex = this.currentOrderProducts.findIndex(
              (x) => x.product_id == data.product_id
          )
          if (findIndex != null) {
            this.currentOrderProducts.splice(findIndex, 1)
          }
        }

        this.attendanceCustomers[index].total_price = this.attendanceCustomers[
            index
            ].products.reduce((a, b) => a + parseFloat(b.total_price), 0)
        if (this.attendanceCustomers[index].promotion_code != null) {
          this.verifyBenefit('promotion', index)
        }
        if (this.attendanceCustomers[index].card_number != null) {
          this.verifyBenefit('membership', index)
        }
        this.updateAttendanceFormQuantity(index)
        this.refreshCustomersAttendance(index)
      }
      this.checkFullDayProduct()
      this.$forceUpdate()
    },
    deleteProduct (data) {
      this.deletedProducts = data
    },
    setCurrentOrderProducts (data) {
      this.currentOrderProducts = data
      this.$forceUpdate()
    },
    checkFullDayProduct () {
      const hasFullDay = this.bookingForm.products.some(
          (obj) => obj.is_full_day === 1
      )

      if (hasFullDay) {
        this.fullDay = true
      } else {
        this.fullDay = false
      }
    },
    verifyBenefit (type, index = null) {
      this.clearBenefit(index)
      if (index === null && this.bookingForm.products.length == 0) {
        this.showError('Please add atleast one product')
        return
      }
      if (
          index !== null &&
          this.attendanceCustomers[index].products &&
          this.attendanceCustomers[index].products.length == 0
      )
      {
        this.showError('Please add atleast one product')
        return
      }
      let data = {
        products: [],
      }
      if (type == 'promotion') {
        if (index === null) {
          data.promotion_code = this.bookingForm.promotion_code
        } else {
          data.promotion_code = this.attendanceCustomers[index].promotion_code
        }
        if (data.promotion_code === null) {
          this.clearBenefit(index)
          return
        }
      } else {
        if (index === null) {
          data.card_number = this.bookingForm.card_number
        } else {
          data.card_number = this.attendanceCustomers[index].card_number
        }
      }
      if (this.bookingForm.mobile && index === null) {
        data.mobile = this.bookingForm.mobile
      } else if (
          index !== null &&
          this.attendanceCustomers[index] &&
          this.attendanceCustomers[index].mobile
      )
      {
        data.mobile = this.attendanceCustomers[index].mobile
      }
      if (index === null) {
        if (this.bookingForm.discount) {
          data.products = []
          this.bookingForm.products.forEach((product) => {
            let pdata = product
            if (product.discount) {
              pdata.price = product.discount.actual_price
              delete pdata.discount
            } else {
              if (product.product_price) {
                pdata.price = product.product_price
              }
            }
            data.products.push(pdata)
          })
        } else {
          data.products = this.bookingForm.products
          data.products.forEach((element) => {
            if (element.product_price) {
              element.price = element.product_price
            } else {
              element.price = element.price / element.quantity
            }
          })
        }
      } else {
        if (this.attendanceCustomers[index].discount) {
          data.products = []
          this.attendanceCustomers[index].products.forEach((product) => {
            let pdata = product
            if (product.discount) {
              pdata.price = product.discount.actual_price
              delete pdata.discount
            } else {
              if (product.product_price) {
                pdata.price = product.product_price
              }
            }
            data.products.push(pdata)
          })
        } else {
          data.products = this.attendanceCustomers[index].products
          data.products.forEach((element) => {
            if (element.product_price) {
              element.price = element.product_price
            } else {
              element.price = element.price / element.quantity
            }
          })
        }
      }

      let url = 'venues/benefits/verify'
      this.$http
          .post(url, data)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              const data = response.data.data
              if (index === null) {
                this.bookingForm.discount = data.discount
                this.bookingForm.price = data.price
                this.bookingForm.total_price = data.total
                data.products.map((product) => {
                  if (product.rental === '1' || product.rental == '1') {
                    product.rental = true
                  } else {
                    product.rental = false
                  }
                })
                this.bookingForm.products = data.products
              } else {
                this.attendanceCustomers[index].discount = data.discount
                this.attendanceCustomers[index].price = data.price
                this.attendanceCustomers[index].products = data.products
                this.attendanceCustomers[index].total_price = data.total
              }
              this.$forceUpdate()
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          })
    },
    clearBenefit (index = null) {
      if (this.bookingForm.discount && index === null) {
        if (this.bookingForm.customer_type != 'member') {
          this.bookingForm.card_number = null
        }
        this.bookingForm.price = this.bookingForm.discount.actual_price
        this.bookingForm.total_price = this.bookingForm.discount.actual_total
        this.bookingForm.products.forEach((product, index) => {
          if (product.discount) {
            this.bookingForm.products[index].price =
                product.discount.actual_price
            this.bookingForm.products[index].total_price =
                product.discount.actual_total
          }
          this.bookingForm.products[index].discount = null
        })
        setTimeout(() => {
          this.bookingForm.discount = null
          this.refreshComponent++
          this.$forceUpdate()
        })
      } else {
        if (
            this.attendanceCustomers[index] &&
            this.attendanceCustomers[index].discount
        )
        {
          if (this.attendanceCustomers[index].customer_type != 'member') {
            this.attendanceCustomers[index].card_number = null
          }
          this.attendanceCustomers[index].price = this.attendanceCustomers[
              index
              ].discount.actual_price
          this.attendanceCustomers[
              index
              ].total_price = this.attendanceCustomers[index].discount.actual_total
          if (
              this.attendanceCustomers[index].products &&
              this.attendanceCustomers[index].products.length > 0
          )
          {
            this.attendanceCustomers[index].products.forEach(
                (product, pIndex) => {
                  if (product.discount) {
                    this.attendanceCustomers[index].products[pIndex].price =
                        product.discount.actual_price
                    this.attendanceCustomers[index].products[pIndex].total_price =
                        product.discount.actual_total
                  }
                  if (
                      this.attendanceCustomers[index].products &&
                      this.attendanceCustomers[index].products[pIndex]
                  )
                  {
                    if (
                        this.attendanceCustomers[index].products[pIndex].discount
                    )
                    {
                      this.$set(
                          this.attendanceCustomers[index],
                          'discount',
                          null
                      )
                    }
                  }
                }
            )
          }
          setTimeout(() => {
            this.attendanceCustomers[index].discount = null
            this.$forceUpdate()
          })
        }
      }
    },
    customerTypeChange (e, index = null) {
      if (index === null) {
        if (this.bookingForm.customer_type == 'corporate') {
          this.getActiveCompanySales()
        }
        if (
            typeof this.bookingForm.promotion_code === 'undefined' ||
            this.bookingForm.promotion_code === null
        )
        {
          this.clearBenefit()
        }
        if (
            this.bookingForm.customer_type == 'normal' &&
            this.bookingForm.card_number != null
        )
        {
          this.$set(this.bookingForm, 'card_number', null)
          this.clearBenefit()
        }
      } else {
        if (this.attendanceCustomers[index].customer_type == 'corporate') {
          this.getActiveCompanySales()
        }
        if (
            typeof this.attendanceCustomers[index].promotion_code ===
            'undefined' ||
            this.attendanceCustomers[index].promotion_code === null
        )
        {
          this.clearBenefit(index)
        }
        if (
            this.attendanceCustomers[index].customer_type == 'normal' &&
            this.attendanceCustomers[index].card_number != null
        )
        {
          this.$set(this.attendanceCustomers[index], 'card_number', null)
          this.clearBenefit(index)
        }
      }
    },
    removeCustomer (index) {
      this.attendanceCustomers.splice(index, 1)
      this.bookedCapacity = this.bookedCapacity - 1
      this.attendanceCustomersTotal.splice(index, 1)
      this.attendanceCustomerAdded.splice(index, 1)
      this.attendanceCustomersAddons.splice(index, 1)
      this.capacityStatus()
    },
    changeStartTime () {
      //console.log('this.bookingForm.start_tim', this.bookingForm.start_time)
      const startTime = moment(this.bookingForm.start_time, 'HH:mm:ss')
      const endTime = startTime.add(this.minBookingTime, 'minutes')
      this.bookingForm.end_time = endTime.format('HH:mm:ss')
      this.getFacilityEndTimes()
    },
    getFacilityEndTimes () {
      this.showLoader('Loading')
      this.endTimes = []
      this.$http
          .get(
              `venues/facilities/bookings/utils?facility_id=${this.facility_id}&date=${this.bookingForm.date}&start_time=${this.bookingForm.start_time}&increment=${this.increment}&min_booking_time=${this.minBookingTime}&booking_id=${this.id}&per_capacity=${this.perCapacity}&venue_service_id=${this.venue_service_id}&enable_overnight_booking=${this.enableOvernightBooking}`
          )
          .then((response) => {
            this.hideLoader()
            if (response.status == 200 && response.data.status == true) {
              this.endTimes = response.data.data.end_times
            }
          })
    },
    getRentalProducts () {
      if (this.currentRepeatDates) {
        this.validateRepeatBookings(this.currentRepeatDates)
      } else {
        this.$http
            .get(
                `venues/facilities/bookings/rentals?start_time=${this.bookingForm.start_time}&end_time=${this.bookingForm.end_time}&facility_id=${this.facility_id}&date=${this.date}&venue_service_id=${this.venue_service_id}`
            )
            .then((response) => {
              if (response.status == 200 && response.data.status == true) {
                const data = response.data.data
                this.productCombinations = data
                if (data.length) {
                  this.changeRentalProducts(data[0])
                }
              }
            })
            .catch((error) => {
              this.errorChecker(error)
            })
      }
    },
    changeRentalProducts (rentalCombination) {
      let otherProducts = []
      if (this.repeatId) {
        otherProducts = this.currentOrderProducts.filter(
            (item) => !item.rental
        )
      }
      if (!this.repeatId) {
        otherProducts = this.bookingForm.products.filter(
            (item) => !item.rental
        )
      }
      this.selectedCombinationPrice = rentalCombination.key
      let rentals = []
      rentalCombination.products.forEach((product) => {
        let tax_amount = product.total_tax_amount ? product.total_tax_amount : product.tax_amount

        if (product.is_repeatable) {
          if (!product.product_price_when_overlapping && !product.total_tax_amount) {
            tax_amount = tax_amount * parseFloat(product.quantity < 1 ? 1 : product.quantity)
          }
          if (product.product_price_when_overlapping && product.price > product.product_price_when_overlapping && !product.total_tax_amount) {
            tax_amount = tax_amount * parseFloat(product.quantity < 1 ? 1 : product.quantity)
            // tax_amount =  tax_amount * parseFloat(product.quantity);
          }
        }

        rentals.push({
          product_id: product.id,
          product_type_id: product.product_type_id,
          price: Number(product.price),
          name: product.name,
          tax: Number(product.tax_amount),
          category_id: product.category_id,
          rental: true,
          product_price: product.product_price_when_overlapping ? product.product_price_when_overlapping : product.product_price,
          quantity: product.quantity,
          total_price: Number(product.price) + Number(tax_amount),
          total_tax_amount: product.total_tax_amount ? product.total_tax_amount : tax_amount,
        })
      })
      if (otherProducts) {
        this.bookingForm.products = [...rentals, ...otherProducts]
      } else {
        this.bookingForm.products = rentals
      }

      this.bookingForm.total_price = this.bookingForm.products.reduce(
          (a, b) => a + parseFloat(b.total_price),
          0
      )

      this.showCombinationDialog = false
      if (this.bookingForm.promotion_code != null) {
        this.verifyBenefit('promotion')
      }
      if (this.bookingForm.card_number != null) {
        this.verifyBenefit('membership')
      }
      this.$forceUpdate()
    },
    chooseRentalCombination () {
      this.showCombinationDialog = true
    },
    getActiveCompanySales () {
      this.$http
          .get(`venues/companies/active/sales?date=${this.date}`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.companies = response.data.data
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          })
    },
    async removeRepeatRow (index) {
      await this.$store
          .dispatch('removeRepeatRow', index)
          .then(() => {
            this.setRepeatData()
          })
          .catch((error) => {
            this.errorChecker(error)
          })
    },
    setRepeatData () {
      this.productCombinations = []
      this.bookingForm.repeats = this.$store.getters.getRepeats
      this.combineRepeatProducts()
    },
    combineRepeatProducts () {
      let otherProducts = this.bookingForm.products.filter(
          (item) => !item.rental
      )
      let rentals = []
      this.bookingForm.repeats.forEach((repeat) => {
        repeat.products.forEach((product) => {
          let index = rentals.findIndex(
              (item) => item.product_id == product.id
          )
          if (index == -1) {

            // let tax_amount =  product.product_price_when_overlapping?product.tax_amount:product.total_tax_amount ? product.total_tax_amount : (product.tax_amount * (product.quantity < 1 ? 1 : product.quantity));

            let tax_amount = product.product_price_when_overlapping ? (product.total_tax_amount ? product.total_tax_amount : (product.tax_amount * (product.quantity < 1 ? 1 : product.quantity))) : (product.total_tax_amount ? product.total_tax_amount : product.tax_amount)

            //let total_price = product.price + tax_amount;

            if (product.product_price_when_overlapping) {
              if (product.price >= product.product_price_when_overlapping) {
                tax_amount = (product.product_price_when_overlapping * 5 / 100) * parseFloat(product.quantity < 1 ? 1 : product.quantity)
              }
              //tax_amount = (product.product_price_when_overlapping * 5 /100) * parseFloat(product.quantity < 1 ? 1 : product.quantity);
            }

            rentals.push({
              product_id: product.id,
              product_type_id: product.product_type_id,
              price: Number(product.price),
              name: product.name,
              tax: Number(product.tax_amount),
              category_id: product.category_id,
              rental: true,
              product_price: Number(product.product_price_when_overlapping ? product.product_price_when_overlapping : product.product_price),
              quantity: product.quantity,
              total_price: Number(product.price) + tax_amount,
              total_tax_amount: Number(product.price) + product.total_tax_amount,

            })
          } else {

            rentals[index].price += Number(product.price)
            rentals[index].quantity += product.quantity
            // rentals[index].total_price += product.price + product.tax_amount * product.quantity;
            rentals[index].total_price += Number(product.price) + Number(product.total_tax_amount ? product.total_tax_amount : product.tax_amount)
            rentals[index].total_tax_amount += Number(product.price) + Number(product.total_tax_amount)

          }
        })
      })

      if (otherProducts) {
        this.bookingForm.products = [...rentals, ...otherProducts]
      } else {
        this.bookingForm.products = rentals
      }
      this.bookingForm.price = this.bookingForm.products.reduce(
          (a, b) => a + parseFloat(b.price),
          0
      )

      // let tax = this.bookingForm.products.reduce(
      //   (a, b) => a + parseFloat(b.tax * b.quantity),
      //   0
      // );

      // this.bookingForm.total_price = this.bookingForm.price + tax;
      this.bookingForm.total_price = this.bookingForm.products.reduce(
          (a, b) => a + parseFloat(b.total_price),
          0
      )
      this.$forceUpdate()
      if (this.bookingForm.promotion_code != null) {
        this.verifyBenefit('promotion')
      }
      if (this.bookingForm.card_number != null) {
        this.verifyBenefit('membership')
      }
    },
    confirmActions (data) {
      if (data.type == 'cancel') {
        this.deleteBookings(data.id)
      } else if (data.type == 'repeatBookingCancel') {
        this.multipleCancelForRepeatBooking()
      } else if (data.type == 'customer') {
        this.bookingCustomersAddons.splice(data.id, 1)
        this.bookingFormAdded--
      } else if (data.type === 'update') {
        this.addOrEditBooking()
      }
      this.$forceUpdate()
      this.confirmModel.id = null
    },

    timeFormat (time) {
      return moment(time, 'HH:mm:ss').format('hh:mm a')
    },

    confirmCancel () {
      this.confirmModel = {
        id: this.order_id,
        title: `Do you want cancel this booking?`,
        description: `This will cancel current booking. By clicking <b>Yes</b> you can confirm cancel operation`,
        type: 'cancel',
      }
    },

    cancelRepeatBooking () {
      if (this.bookingWithOpenProduct) {
        this.confirmCancel()
      } else if (
          this.repeatDatesForPayments.filter((element) => element.isPaid).length >
          0
      )
      {
        this.confirmModel = {
          id: this.order_id,
          title: `Do you want cancel this booking?`,
          description: `This will cancel current booking. By clicking <b>Yes</b> you can confirm cancel operation`,
          type: 'repeatBookingCancel',
        }
      } else {
        this.showError('Please select date for cancel')
      }
    },

    deleteBookings (id) {
      this.showLoader('Wait')
      this.$http
          .delete(`venues/orders/${id}`)
          .then((response) => {
            this.hideLoader()
            if (response.status == 200 && response.data.status == true) {
              this.showSuccess('Booking cancelled successfully')
              this.close()
              this.$emit('cancel')
            }
          })
          .catch((error) => {
            this.hideLoader()
            this.errorChecker(error)
          })
    },

    changeRepeatBookingSwitch () {
      if (!this.bookingForm.repeat) {
        this.currentRepeatDates = null
        this.getRentalProducts()
        this.bookingForm.repeats = []
      } else {
        this.$store.commit('resetRepeatState')
        this.$store
            .dispatch('addRepeatRow', {
              start_time: this.bookingForm.start_time,
              end_time: this.bookingForm.end_time,
              date: this.date,
              facility_id: this.facility_id,
              init: true,
            })
            .catch((error) => {
              this.errorChecker(error)
            })
        this.bookingForm.repeats = []
      }
      this.$forceUpdate()
    },

    addRepeatRow () {
      this.$store
          .dispatch('addRepeatRow', {
            start_time: null,
            end_time: null,
            date: this.date,
            facility_id: this.facility_id,
            init: true,
          })
          .catch((error) => {
            this.errorChecker(error)
          })
    },

    showInvoice () {
      if (
          this.bookingForm.parent_order_id &&
          this.bookingForm.parent_orders_id.length > 0
      )
      {
        this.$emit(
            'repeatBookingReceipt',
            this.bookingForm.parent_orders_id.sort((a, b) => b - a)
        )
      } else {
        this.$emit('pay', this.bookingForm.order_id)
      }
    },
    addAttandance () {
      this.attendanceCustomers.push({
        opt_marketing: false,
        productCategoryId: null,
        quantity: 1,
        price: 0,
        total_price: 0,
        selectedProduct: { quantity: 1, price: 0 },
        products: new Array(),
        discount: null,
        promotion_code: null,
        additional_fields:[],
      })
      this.bookedCapacity = this.bookedCapacity + 1
      this.attendanceCustomersTotal.push(0)
      this.attendanceCustomerAdded.push(1)
      this.attendanceCustomersAddons.push([])
      this.productDetailsExpansion = this.attendanceCustomers.length;
      this.$forceUpdate()
      this.capacityStatus()
    },
    capacityStatus () {
      if (this.bookedCapacity >= this.capacity) {
        this.addAttandanceBtn = false
      } else {
        this.addAttandanceBtn = true
      }
      this.$forceUpdate()
    },
    changeAttendanceSwitch () {
      this.attendanceData = new Array()
      if (this.bookingForm.attendance == false) {
        this.bookingForm.attendance_count = 1
        this.attendanceCustomers = [
          {
            opt_marketing: false,
            productCategoryId: null,
            quantity: 1,
            price: 0,
            total_price: 0,
            selectedProduct: { quantity: 1, price: 0 },
            products: new Array(),
            discount: null,
            promotion_code: null,
          },
        ]
        this.attendanceData = new Array()
      } else {
        this.bookedCapacity = 1 + this.totalParticipants
      }

      this.$forceUpdate()
    },
    showRepeatPaymentsConfirmation () {
      let unpaidOrderLength = this.repeatDatesForPayments.filter(
          (element) => element.isPaid && element.status_id == 5
      ).length
      let totalActiveOrderLength = this.repeatDatesForPayments.filter(
          (element) => element.status_id != 2
      ).length
      let paidOrderLength = this.repeatDatesForPayments.filter(
          (element) => element.isPaid && element.status_id == 1
      ).length
      if (unpaidOrderLength == totalActiveOrderLength) {
        // console.log("1");
        this.$emit('pay', this.bookingForm.order_id)
      } else if (paidOrderLength > 0) {
        // console.log("2");
        this.showError('Please unselect paid date')
      } else if (unpaidOrderLength > 0) {
        // console.log("3: " + unpaidOrderLength);
        var findUnpaidOrders = this.repeatDatesForPayments.filter(
            (x) => x.status_id == 5
        )
        if (
            findUnpaidOrders.length ==
            this.repeatDatesForPayments.filter(
                (element) => element.isPaid && element.status_id == 5
            ).length
        )
        {
          // console.log("4 ");
          this.$emit('pay', findUnpaidOrders[0].order_id)
        } else {
          // console.log("5");
          this.setPayerCustomerList()
          this.multiplePayment()
        }
      } else {
        this.showError('Please select date for pay')
      }
    },

    multiplePayment () {
      if (this.bookingForm.products.length == 0) {
        this.showError('Please add at least one product')
        return
      }
      if (
          this.repeatDatesForPayments.filter(
              (element) => element.isPaid && element.status_id == 5
          ).length > 0
      )
      {
        this.enableRepeatOrderPay = true
        this.repeatBookingIds = []
        this.repeatDatesForPayments.forEach((el) => {
          if (el.isPaid && el.status_id == 5) {
            this.repeatBookingIds.push(el)
          }
        })
      }
      this.$forceUpdate()
    },

    multipleCancelForRepeatBooking () {
      let bookingIds = []

      this.repeatDatesForPayments.forEach((el) => {
        if (el.isPaid) {
          bookingIds.push({
            booking_id: el.booking_id,
            date: el.date,
            order_id: el.order_id,
            status_id: el.status_id,
            facility_id: el.facility_id,
            end_time: el.end_time,
            start_time: el.start_time,
          })
        }
      })
      this.showLoader()
      this.$http
          .post(`venues/facilities/bookings/repeat/multiple/cancel`, {
            booking_ids: bookingIds,
          })
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.showSuccess('Booking cancelled successfully')
              this.close()
              this.$emit('cancel')
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          })
    },

    repeatDatesForPaymentChange (dates, rentalCombination) {
      this.repeatDatesForPayments = dates
      if (dates.filter((element) => element.isPaid).length == 1) {
        this.isEnableRepeatBookingPayment = true
        this.isEnableRepeateBookingReschedule = true
      } else if (dates.filter((element) => element.isPaid).length == 0) {
        this.isEnableRepeatBookingPayment = false
        this.isEnableRepeateBookingReschedule = false
      } else {
        this.isEnableRepeatBookingPayment = true
        this.isEnableRepeateBookingReschedule = true
      }

      if (rentalCombination) {
        this.changeRentalProducts(rentalCombination)
        if (this.bookingForm.promotion_code != null) {
          this.verifyBenefit('promotion')
        }
        if (this.bookingForm.card_number != null) {
          this.verifyBenefit('membership')
        }
      } else {
        this.changeRentalProducts({ products: [] })
      }
    },
    searchMember (mobile, id, first_name, last_name, index = null) {
      this.isSearchLoading = true
      let query = ''
      query = `field=id&search=${id}`
      this.$http
          .get(`venues/memberships/members/filters?${query}`)
          .then((response) => {
            if (response.status == 200) {
              let data = response.data.data
              if (data.length > 0) {
                if (data[0].card_number) {
                  if (index === null) {
                    this.bookingForm.customer_type = 'member'
                    this.$set(
                        this.bookingForm,
                        'card_number',
                        data[0].card_number
                    )
                    this.$set(
                        this.bookingForm,
                        'membership_id',
                        data[0].membership_id
                    )
                    this.$forceUpdate()
                    if (
                        this.bookingForm &&
                        this.bookingForm.products.length > 0
                    )
                    {
                      this.verifyBenefit('membership')
                    }
                  } else {
                    this.$set(
                        this.attendanceCustomers[index],
                        'customer_type',
                        'member'
                    )
                    this.$set(
                        this.attendanceCustomers[index],
                        'card_number',
                        data[0].card_number
                    )
                    this.$set(
                        this.attendanceCustomers[index],
                        'membership_id',
                        data[0].membership_id
                    )
                    this.$forceUpdate()
                    if (
                        this.attendanceCustomers[index] &&
                        this.attendanceCustomers[index].products &&
                        this.attendanceCustomers[index].products.length > 0
                    )
                    {
                      this.verifyBenefit('membership', index)
                    }
                  }
                } else {
                  if (index === null) {
                    if (this.bookingForm.customer_type == 'member') {
                      this.bookingForm.customer_type = 'normal'
                      this.$set(this.bookingForm, 'customer_type', 'normal')
                      this.$set(this.bookingForm, 'membership_id', null)
                    }
                  } else {
                    if (
                        this.attendanceCustomers[index].customer_type == 'member'
                    )
                    {
                      this.attendanceCustomers[index].customer_type = 'normal'
                      this.attendanceCustomers[index].membership_id = null
                    }
                  }
                  this.clearCardAndBenefits(index)
                }
              } else {
                if (index === null) {
                  if (this.bookingForm.customer_type == 'member') {
                    this.bookingForm.customer_type = 'normal'
                    this.$set(this.bookingForm, 'customer_type', 'normal')
                    this.$set(this.bookingForm, 'membership_id', null)
                  }
                } else {
                  if (this.attendanceCustomers[index].customer_type == 'member') {
                    this.attendanceCustomers[index].customer_type = 'normal'
                    this.attendanceCustomers[index].membership_id = null
                  }
                }
                this.clearCardAndBenefits(index)
              }
              this.$forceUpdate()
            } else {
              this.clearCardAndBenefits(index)
            }
          })
          .catch((error) => {
            this.errorChecker(error)
            this.clearCardAndBenefits(index)
          })
    },
    clearCardAndBenefits (attCustomerIndex = null) {
      if (attCustomerIndex === null) {
        this.bookingForm.member = null
        this.bookingForm.customer_type = this.bookingForm.customer_type
            ? this.bookingForm.customer_type
            : null
        this.$set(this.bookingForm, 'card_number', null)
        if (this.bookingForm.promotion_code == null) {
          this.clearBenefit()
        }
      } else {
        if (this.attendanceCustomers[attCustomerIndex]) {
          this.attendanceCustomers[attCustomerIndex].customer_type = this
              .attendanceCustomers[attCustomerIndex].customer_type
              ? this.attendanceCustomers[attCustomerIndex].customer_type
              : null
          this.$set(
              this.attendanceCustomers[attCustomerIndex],
              'card_number',
              null
          )
          if (
              this.attendanceCustomers[attCustomerIndex].promotion_code == null
          )
          {
            this.clearBenefit(attCustomerIndex)
          }
          // this.clearBenefit(attCustomerIndex);
        }
      }
      this.$forceUpdate()
    },
    checkSlotCapacity () {
      if (parseInt(this.bookingForm.attendance_count) <= 0) {
        this.bookingForm.attendance_count = 1
        return
      }

      let totalAttendance
      if (this.id > 0) {
        totalAttendance =
            parseInt(this.bookingForm.attendance_count) +
            (this.bookedCapacity ? this.bookedCapacity - this.attendies : 0)
      } else {
        totalAttendance =
            parseInt(this.bookingForm.attendance_count) +
            (this.bookedCapacity ? this.bookedCapacity : 0)
      }

      if (totalAttendance > this.capacity) {
        this.bookingForm.attendance_count = 1
        this.showError(
            `Only ${this.capacity -
            (this.attendies
                ? this.bookedCapacity - this.attendies
                : this.bookedCapacity)} slot available `
        )
      }
    },
    setCustomerProduct (index = null, products) {
      console.log("hello",products);
      console.log("this.bookingForm",this.bookingForm);
      if (index === null) {
        this.bookingForm.products = [...products]
        this.checkFullDayProduct()
        if (this.bookingForm.promotion_code != null) {
          this.verifyBenefit('promotion')
        }
        if (this.bookingForm.card_number != null) {
          this.verifyBenefit('membership')
        }

        this.bookingForm.total_price = products.reduce(
            (a, b) => a + parseFloat(b.total_price),
            0
        )
        this.refreshComponent++
        this.updateBookingFormQuantity()
      } else {
        this.attendanceCustomers[index].products = products
        if (
            this.attendanceCustomers[index].promotion_code &&
            this.attendanceCustomers[index].promotion_code != null
        )
        {
          this.verifyBenefit('promotion', index)
        }
        if (
            this.attendanceCustomers[index].card_number &&
            this.attendanceCustomers[index].card_number != null
        )
        {
          this.verifyBenefit('membership', index)
        }

        this.attendanceCustomers[index].total_price = products.reduce(
            (a, b) => a + parseFloat(b.total_price),
            0
        )
        this.updateAttendanceFormQuantity(index)
      }

      this.$forceUpdate()
    },
    setCustomerDataAddon (data, key, index = null) {
      if (data.isEmiratesIdCheck) {
        this.isEmiratesIdCheck = true
      }


      if (index === null) {
        if (data.mobile && data.first_name && data.customer_id) {
          this.isEmiratesIdCheck = false
          this.searchMember(
              data.mobile,
              data.customer_id,
              data.first_name,
              data.last_name
          )
        } else {
          this.clearCardAndBenefits()
        }
        if (data.additional_data && data.additional_data.length) {
          this.$set(this.bookingCustomersAddons[key], 'additional_fields', this.mapAdditionalDataToFields(data.additional_data));
        }
        // Handle additional_fields updates from CustomerBookingForm (e.g., signature saves)
        if (data.additional_fields && data.additional_fields.length) {
          this.$set(this.bookingCustomersAddons[key], 'additional_fields', data.additional_fields);
        }
        if (!data.customer_id && this.id > 0) {
          this.$set(this.bookingCustomersAddons[key], 'customer_id', null)
        }

        if (!data.name && data.first_name) {
          this.$set(this.bookingCustomersAddons[key], 'name', data.first_name)
        }
        if (
            this.bookingCustomersAddons[key].customer_id &&
            !data.customer_id &&
            this.bookingCustomersAddons[key].name != data.name &&
            this.bookingCustomersAddons[key].mobile != data.mobile
        )
        {
          this.$set(this.bookingCustomersAddons[key], 'mobile', null)
          this.bookingCustomersAddons[key].search = null
          this.bookingCustomersAddons[key].nameSearch = null
          this.$set(this.bookingCustomersAddons[key], 'email', null)
          this.$set(this.bookingCustomersAddons[key], 'gender', null)
          this.$set(this.bookingCustomersAddons[key], 'name', null)
          this.$set(this.bookingCustomersAddons[key], 'customer_id', null)
          this.$set(this.bookingCustomersAddons[key], 'first_name', null)
          this.$set(this.bookingCustomersAddons[key], 'image_path', null)
          this.$set(this.bookingCustomersAddons[key], 'dob', null)
          this.$set(this.bookingCustomersAddons[key], 'age_group', null)
          this.$set(this.bookingCustomersAddons[key], 'country_id', null)
          this.$set(this.bookingCustomersAddons[key], 'last_name', null)
          this.$set(this.bookingCustomersAddons[key], 'opt_marketing', false)
          this.$set(this.bookingCustomersAddons[key], 'id_proof_type_id', null)
          this.$set(this.bookingCustomersAddons[key], 'id_proof_number', null)
          this.$set(this.bookingCustomersAddons[key], 'id_proof_path', null)
          this.$forceUpdate()
        }
        if (data.mobile)
          this.$set(this.bookingCustomersAddons[key], 'mobile', data.mobile)
        if (data.email)
          this.$set(this.bookingCustomersAddons[key], 'email', data.email)
        if (data.country_id) {
          this.$set(
              this.bookingCustomersAddons[key],
              'country_id',
              data.country_id
          )
        } else {
          if (!this.isEmiratesIdCheck && data.customer_id) {
            this.$set(this.bookingCustomersAddons[key], 'country_id', null)
          }
        }
        if (data.gender) {
          this.$set(this.bookingCustomersAddons[key], 'gender', data.gender)
        } else {
          if (!this.isEmiratesIdCheck && data.customer_id) {
            this.$set(this.bookingCustomersAddons[key], 'gender', null)
          }
        }
        if (data.dob) {
          this.$set(this.bookingCustomersAddons[key], 'dob', data.dob)
        } else {
          if (!this.isEmiratesIdCheck && data.customer_id) {
            this.$set(this.bookingCustomersAddons[key], 'dob', null)
          }
        }
        if (data.age_group) {
          this.$set(this.bookingCustomersAddons[key], 'age_group', data.age_group)
        } else {
          if (!this.isEmiratesIdCheck && data.customer_id) {
            this.$set(this.bookingCustomersAddons[key], 'age_group', null)
          }
        }
        if (data.name) {
          data.name = data.name.replace(/\s\s+/g, ' ')
          data.name = data.name.trim()
          this.$set(this.bookingCustomersAddons[key], 'name', data.name)
        }
        if (data.last_name) {
          data.last_name = data.last_name.replace(/\s\s+/g, ' ')
          data.last_name = data.last_name.trim()
          this.$set(
              this.bookingCustomersAddons[key],
              'last_name',
              data.last_name
          )
        } else {
          this.$set(this.bookingCustomersAddons[key], 'last_name', null)
        }
        if (data.first_name) {
          data.first_name = data.first_name.replace(/\s\s+/g, ' ')
          data.first_name = data.first_name.trim()
          this.$set(
              this.bookingCustomersAddons[key],
              'first_name',
              data.first_name
          )
        }
        if (data.customer_id)
          this.$set(
              this.bookingCustomersAddons[key],
              'customer_id',
              data.customer_id
          )
        if (data.image_path) {
          this.$set(
              this.bookingCustomersAddons[key],
              'image_path',
              data.image_path
          )
        } else {
          this.$set(this.bookingCustomersAddons[key], 'image_path', null)
        }
        if (data.id_proof_type_id) {
          this.$set(
              this.bookingCustomersAddons[key],
              'id_proof_type_id',
              data.id_proof_type_id
          )
        } else {
          if (!this.isEmiratesIdCheck && data.customer_id) {
            this.$set(
                this.bookingCustomersAddons[key],
                'id_proof_type_id',
                null
            )
          }
        }
        if (data.id_proof_number) {
          this.$set(
              this.bookingCustomersAddons[key],
              'id_proof_number',
              data.id_proof_number
          )
        } else {
          if (!this.isEmiratesIdCheck && data.customer_id) {
            this.$set(
                this.bookingCustomersAddons[key],
                'id_proof_number',
                null
            )
          }
        }
        if (data.id_proof_path) {
          this.$set(
              this.bookingCustomersAddons[key],
              'id_proof_path',
              data.id_proof_path
          )
        } else {
          if (!this.isEmiratesIdCheck && data.customer_id) {
            this.$set(this.bookingCustomersAddons[key], 'id_proof_path', null)
          }
        }

        if (data.id_proof) {
          this.$set(
              this.bookingCustomersAddons[key],
              'id_proof',
              data.id_proof
          )
        } else {
          if (!this.isEmiratesIdCheck && data.customer_id) {
            this.$set(this.bookingCustomersAddons[key], 'id_proof', null)
          }
        }
        if (data.opt_marketing) {
          if (data.opt_marketing == 1) {
            this.$set(this.bookingCustomersAddons[key], 'opt_marketing', true)
          } else {
            this.$set(this.bookingCustomersAddons[key], 'opt_marketing', false)
          }
        }
        if (data.customer_documents) {
          this.bookingCustomersAddons[key].customer_documents =
              data.customer_documents
          if (
              data.customer_documents[0] &&
              data.customer_documents[0].id_proof_type_id
          )
          {
            this.$set(
                this.bookingCustomersAddons[key],
                'id_proof_type_id',
                data.customer_documents[0].id_proof_type_id
            )
          }
          if (
              data.customer_documents[0] &&
              data.customer_documents[0].id_proof_number
          )
          {
            this.$set(
                this.bookingCustomersAddons[key],
                'id_proof_number',
                data.customer_documents[0].id_proof_number
            )
          }
          if (
              data.customer_documents[0] &&
              data.customer_documents[0].id_proof_path
          )
          {
            this.$set(
                this.bookingCustomersAddons[key],
                'id_proof_path',
                data.customer_documents[0].id_proof_path
            )
          }
        } else {
          if (data.customer_id) {
            this.bookingCustomersAddons[key].customer_documents = []
          }
        }
        if (data.customer_tag) {
          this.$set(
              this.bookingCustomersAddons[key],
              'customer_tag',
              data.customer_tag
          )
        } else {
          this.$set(this.bookingCustomersAddons[key], 'customer_tag', null)
        }
      } else {
        if (data.additional_data && data.additional_data.length) {
          this.$set(this.bookingCustomersAddons[key], 'additional_fields', data.additional_data);
        }
        if (data.mobile && data.first_name && data.customer_id) {
          this.isEmiratesIdCheck = false
          this.searchMember(
              data.mobile,
              data.customer_id,
              data.first_name,
              data.last_name,
              key
          )
        } else {
          this.clearCardAndBenefits(index)
        }

        if (!data.customer_id) {
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'customer_id',
              null
          )
        }

        if (!data.name && data.first_name) {
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'name',
              data.first_name
          )
        }
        if (
            this.attendanceCustomersAddons[key][index].customer_id &&
            !data.customer_id &&
            this.attendanceCustomersAddons[key][index].name != data.name &&
            this.attendanceCustomersAddons[key][index].mobile != data.mobile
        )
        {
          this.$set(this.attendanceCustomersAddons[key][index], 'mobile', null)
          this.attendanceCustomersAddons[key][index].search = null
          this.attendanceCustomersAddons[key][index].nameSearch = null
          this.$set(this.attendanceCustomersAddons[key][index], 'email', null)
          this.$set(this.attendanceCustomersAddons[key][index], 'gender', null)
          this.$set(this.attendanceCustomersAddons[key][index], 'name', null)
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'customer_id',
              null
          )
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'first_name',
              null
          )
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'image_path',
              null
          )
          this.$set(this.attendanceCustomersAddons[key][index], 'dob', null)
          this.$set(this.attendanceCustomersAddons[key][index], 'age_group', null)
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'country_id',
              null
          )
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'last_name',
              null
          )
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'opt_marketing',
              false
          )
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'id_proof_type_id',
              null
          )
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'id_proof_number',
              null
          )
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'id_proof_path',
              null
          )
          this.$forceUpdate()
        }
        if (data.mobile)
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'mobile',
              data.mobile
          )
        if (data.email)
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'email',
              data.email
          )
        if (data.country_id) {
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'country_id',
              data.country_id
          )
        } else {
          if (!this.isEmiratesIdCheck) {
            this.$set(
                this.attendanceCustomersAddons[key][index],
                'country_id',
                null
            )
          }
        }
        if (data.gender) {
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'gender',
              data.gender
          )
        } else {
          if (!this.isEmiratesIdCheck) {
            this.$set(
                this.attendanceCustomersAddons[key][index],
                'gender',
                null
            )
          }
        }
        if (data.dob) {
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'dob',
              data.dob
          )
        } else {
          if (!this.isEmiratesIdCheck) {
            this.$set(this.attendanceCustomersAddons[key][index], 'dob', null)
          }
        }
        if (data.age_group) {
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'age_group',
              data.age_group
          )
        } else {
          if (!this.isEmiratesIdCheck) {
            this.$set(this.attendanceCustomersAddons[key][index], 'age_group', null)
          }
        }
        if (data.name) {
          data.name = data.name.replace(/\s\s+/g, ' ')
          data.name = data.name.trim()
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'name',
              data.name
          )
        }
        if (data.last_name) {
          data.last_name = data.last_name.replace(/\s\s+/g, ' ')
          data.last_name = data.last_name.trim()
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'last_name',
              data.last_name
          )
        } else {
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'last_name',
              null
          )
        }
        if (data.first_name) {
          data.first_name = data.first_name.replace(/\s\s+/g, ' ')
          data.first_name = data.first_name.trim()
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'first_name',
              data.first_name
          )
        }
        if (data.customer_id)
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'customer_id',
              data.customer_id
          )
        if (data.image_path) {
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'image_path',
              data.image_path
          )
        } else {
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'image_path',
              null
          )
        }
        if (data.id_proof_type_id) {
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'id_proof_type_id',
              data.id_proof_type_id
          )
        } else {
          if (!this.isEmiratesIdCheck) {
            this.$set(
                this.attendanceCustomersAddons[key][index],
                'id_proof_type_id',
                null
            )
          }
        }
        if (data.id_proof_number) {
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'id_proof_number',
              data.id_proof_number
          )
        } else {
          if (!this.isEmiratesIdCheck) {
            this.$set(
                this.attendanceCustomersAddons[key][index],
                'id_proof_number',
                null
            )
          }
        }
        if (data.id_proof_path) {
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'id_proof_path',
              data.id_proof_path
          )
        } else {
          if (!this.isEmiratesIdCheck) {
            this.$set(
                this.attendanceCustomersAddons[key][index],
                'id_proof_path',
                null
            )
          }
        }

        if (data.id_proof) {
          this.$set(
              this.attendanceCustomersAddons[key][index],
              'id_proof',
              data.id_proof
          )
        } else {
          if (!this.isEmiratesIdCheck) {
            this.$set(
                this.attendanceCustomersAddons[key][index],
                'id_proof',
                null
            )
          }
        }
        if (data.opt_marketing) {
          if (data.opt_marketing == 1) {
            this.$set(
                this.attendanceCustomersAddons[key][index],
                'opt_marketing',
                true
            )
          } else {
            this.$set(
                this.attendanceCustomersAddons[key][index],
                'opt_marketing',
                false
            )
          }
        }
        if (data.customer_documents) {
          this.attendanceCustomersAddons[key][index].customer_documents =
              data.customer_documents
          if (
              data.customer_documents[0] &&
              data.customer_documents[0].id_proof_type_id
          )
          {
            this.$set(
                this.attendanceCustomersAddons[key][index],
                'id_proof_type_id',
                data.customer_documents[0].id_proof_type_id
            )
          }
          if (
              data.customer_documents[0] &&
              data.customer_documents[0].id_proof_number
          )
          {
            this.$set(
                this.attendanceCustomersAddons[key][index],
                'id_proof_number',
                data.customer_documents[0].id_proof_number
            )
          }
          if (
              data.customer_documents[0] &&
              data.customer_documents[0].id_proof_path
          )
          {
            this.$set(
                this.attendanceCustomersAddons[key][index],
                'id_proof_path',
                data.customer_documents[0].id_proof_path
            )
          }
        } else {
          this.attendanceCustomersAddons[key][index].customer_documents = []
        }
        if (data.customer_tag) {
          this.$set(
              this.bookingCustomersAddons[key][index],
              'customer_tag',
              data.customer_tag
          )
        } else {
          this.$set(
              this.bookingCustomersAddons[key][index],
              'customer_tag',
              null
          )
        }


      }
      this.$refs.form.resetValidation()
      this.$forceUpdate()
    },
    setPayerCustomerList () {
      this.payerCustomerList = []
      if (this.bookingForm.customer_id) {
        this.payerCustomerList.push({
          customerId: this.bookingForm.customer_id,
          customerName: this.bookingForm.name,
          mobile: this.bookingForm.mobile,
          email: this.bookingForm.email,
          cashWallet: this.wallet.cash,
        })
      }
    },

    approveBooking () {
      this.showLoader('Loading')
      this.$http
          .get(
              `venues/facilities/bookings/booking-status/${this.bookingForm.id}/approve`
          )
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.hideLoader()
              this.$emit('refresh')
              this.showInvoice()
            }
          })
          .catch((error) => {
            this.hideLoader()
            this.errorChecker(error)
          })
    },
    getFacilities () {
      this.$http.get(`venues/facilities/short?venue_service_id=${this.venue_service_id}&per_capacity=0&type=ground`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.facilities = response.data.data
              this.$forceUpdate()
            }
          })
          .catch((error) => {
            this.errorChecker(error)
          })
    },
    calculateHeight(){
      setTimeout(() => {
        const element = document.querySelector('.booking-modal');
        if (element){
          const height = element.getBoundingClientRect().height;
          console.log(height);
          this.maxHeight = height;
        }
      },1000)
    },

    // Helper method to map additional_data from API to proper additional_fields structure
    mapAdditionalDataToFields(additionalData) {
      if (!this.additionalFields || !this.additionalFields.length) {
        return [];
      }

      const result = this.additionalFields.map(field => {
        if (!field?.id) return { ...field, value: field.type === 'check_boxes' ? [] : '' };

        const matched = additionalData.find(f => f.id === field.id);

        if (!matched) {
          return { ...field, value: field.type === 'check_boxes' ? [] : '' };
        }

        let value;
        if (field.type === 'check_boxes') {
          if (Array.isArray(matched.value)) {
            value = matched.value;
          } else {
            try {
              value = JSON.parse(matched.value);
            } catch {
              value = [];
            }
          }
        } else {
          value = matched.value;
        }

        return { ...field, value };
      }).filter(Boolean);

      return result;
    },

    // Helper method to get default additional fields structure
    getDefaultAdditionalFields() {
      if (!this.additionalFields || !this.additionalFields.length) {
        return [];
      }

      return this.additionalFields.map(field => ({
        ...field,
        value: field.type === 'check_boxes' ? [] : ''
      }));
    },
  },
}
</script>
<style lang="scss" scoped>
.product-box{
  max-height: 500px;
  @media screen and (max-height: 1095px){
    max-height: 380px;
  }
}
.toggle-switch-label {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  text-align: left;
  color: #112A46
}

$card-outlined-border-width: 3px;
.open-product {
  background-color: teal;
  cursor: pointer;
  height: 30px;
  width: 60px;
  color: #fff;
  font-size: 12px;
  font-weight: bold;
  padding: 5px;
  transition: "border-radius 200ms ease-in-out";
  border-radius: 4px;
  text-align: center;
}
</style>
<style lang="scss">
.customer-details {
  .v-expansion-panel {
    border-radius: 0.25rem;
    border: 1px solid #EAEAEA;

    &::before {
      box-shadow: unset !important;
    }
  }

  .panel-header {
    background: #F8FAFB;
    color: #0F2A4D !important;
    min-height:36px;
    .v-icon {
      color: #0F2A4D !important;
    }
  }
  .active > .panel-header {
    color: #fff !important;
    background: #112A46;

    .v-icon {
      color: #fff;
    }
  }

}

.order-summary-box {
  margin-top: auto;
  margin-bottom: 0.5rem;
  //position: absolute;
  bottom: 10px;
  width: 100%;
  .panel {
    &::before {
      box-shadow: unset !important;
    }
  }

  .panel-header {
    background-color: rgba(79, 174, 175, 0.10) !important;
  }
}
.booking-modal{
  max-height: 100%;
  background-color: #FFFFFF;
}
.booking-form{
  //max-height: 900px;
  //@media screen and (max-height: 1096px) {
  //  max-height: 850px;
  //}
  .customer-details{
    padding-bottom: 40px;
    //max-height: 800px;
    //@media screen and (max-height: 1096px) {
    //  max-height: 750px;
    //}
  }
}


</style>
