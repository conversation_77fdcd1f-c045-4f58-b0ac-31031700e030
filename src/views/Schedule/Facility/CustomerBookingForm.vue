<template>

  <v-expansion-panels v-model="openPanel" class="border-0 my-4">
    <v-expansion-panel>
      <v-expansion-panel-header class="p-0" hide-actions style="min-height: 44px !important;">
        <template v-slot:default="{ open }">
          <div :class="{'py-1':open,'py-3':!open}"
               class="d-flex justify-space-between align-center bg-neon opacity-10 px-4 w-full">
            <div class="title-text">{{ bookingForm.name || 'Customer Name' }}</div>
            <div class="d-flex align-center gap-x-2">
              <card-data-button
                  :outlined="false"
                  :plain="true"
                  class-name="blue-text px-0 reader-button"
                  label="HID Omnikey"
                  @data="(data) => { setCardData(data); }"
              >
                <EmirateIdReaderIcon/>
              </card-data-button>
              <card-reader-button
                  :outlined="false"
                  :plain="true"
                  className="blue-text px-0 reader-button"
                  label="Samsotech Reader"
                  @data="
            (data) => {
              setCardData(data);
            }
          "
              >
                <SamosoTechReaderIcon/>
              </card-reader-button>
              <MinusIcon v-if="open"/>
              <PlusIcon v-else/>
            </div>
          </div>
        </template>

      </v-expansion-panel-header>
      <v-expansion-panel-content>
        <div class="d-flex justify-space-between align-center w-full">
          <div v-if="!addOn">
            <div v-if="perCapacity == 1" class="pa-2 text-sm text-center">
              Price
              {{ bookingForm.total_price | toCurrency }}
              <span
                  v-if="
              bookingForm.discount != null &&
                bookingForm.price != bookingForm.discount.actual_price
            "
                  class="text-decoration-line-through"
              >
            {{ bookingForm.discount.actual_total | toCurrency }}</span
              >
            </div>
          </div>
          <div
              v-if="promotions.length > 0 && perCapacity == 1 && !addOn"
              class="mr-2"
              style="margin-bottom: 10px"
          >
            <label v-if="!bookingForm.card_number" for="">Promotions</label>
            <v-autocomplete
                v-if="!bookingForm.card_number"
                v-model="bookingForm.promotion_code"
                :items="[{ name: 'None', promotion_code: null }, ...promotions]"
                :readonly="disablePromotion"
                class="q-autocomplete shadow-0"
                dense
                hide-details="auto"
                item-text="name"
                item-value="promotion_code"
                label=""
                outlined
                style="width: 200px"
                @change="verifyBenefit('promotion')"
            >
            </v-autocomplete>
          </div>
        </div>
        <v-tooltip v-if="addOn == 1" bottom>
          <template v-slot:activator="{ on, attrs }">
            <v-btn
                absolute
                fab
                top
                v-bind="attrs"
                x-small
                elevation="0"
                style="right: 0"
                @click="deleteCustomer()"
                v-on="on"
            >
              <DeleteIcon/>
            </v-btn>
          </template>
          Delete
        </v-tooltip>
        <v-row align="center" class="px-4 pt-4 pb-0 border-bottom mx-0">
          <v-col md="6" lg="4" class="py-2" v-if="!addOn">
            <v-radio-group
                v-model="bookingForm.customer_type"
                class="custom-radio-group border-0"
                row
                @change="customerTypeChange"
                mandatory
                hide-details="auto"
                :readonly="id > 0"
            >
              <v-radio label="Normal" class="custom-radio border-0 px-0 bg-white" color="cyan" value="normal"></v-radio>
              <!-- <v-radio label="Corporate" color="cyan" value="corporate"></v-radio> -->
              <v-radio label="Member" class="custom-radio border-0 px-0 bg-white" color="cyan" value="member"></v-radio>
            </v-radio-group>
          </v-col>
          <v-col v-if="bookingForm.customer_type == 'member' && !addOn" class="py-2" md="3">
            <v-member-search
                v-model="bookingForm.member"
                :selected="bookingForm.card_number"
                @clear="clearBenefit"
                @updateCustomer="setMemberData"
            >
            </v-member-search>
          </v-col>
          <!-- <v-col md="2" v-if="bookingForm.customer_type == 'corporate' && !addOn">
            <v-autocomplete
              class="mt-4"
              label="Company Name"
              :items="companies"
              v-model="bookingForm.company_id"
              item-text="name"
              item-value="id"
              outlined
              background-color="#fff"
              dense
            >
            </v-autocomplete>
          </v-col>
          <v-col md="2" v-if="bookingForm.customer_type == 'corporate' && !addOn">
            <v-autocomplete
              :disabled="bookingForm.company_id == null"
              :items="getCompanySales()"
              label="Sale Order ID"
              item-text="sale_seq_no"
              item-value="id"
              class="mt-4 ml-2"
              v-model="bookingForm.company_sale_id"
              outlined
              background-color="#fff"
              dense
            >
            </v-autocomplete>
          </v-col> -->
          <v-spacer></v-spacer>
          <v-col class="d-flex justify-end align-center py-2" md="4">
            <CustomSwitch
                v-model="bookingForm.opt_marketing"
                :model-value="bookingForm.opt_marketing"
                :show-label="true"
                class-name="d-flex flex-row-reverse"
                label="Opt In Marketing"
                @update:modelValue="(val)=>{
                  bookingForm.opt_marketing=val;
                }"
            />
          </v-col>
        </v-row>
        <v-row class="px-4 pb-4 odd-bordered-row">
          <v-col md="6" sm="12">
            <div>
              <label for="">Mobile No*</label>
              <v-mobile-search
                  v-model="bookingForm.search"
                  :dense="true"
                  :selected="bookingForm.mobile"
                  :show-label="false"
                  background-color=""
                  class-name1="q-text-field shadow-0"
                  hide-details="auto"
                  label=""
                  outlined
                  readonly
                  :venueServiceId="venue_service_id"
                  :productTypeId="6"
                  @updateCustomer="setCustomerData"
              ></v-mobile-search>
            </div>
          </v-col>

          <v-col md="6" sm="12">
            <div>
              <label for="">Name*</label>
              <v-name-search
                  v-model="bookingForm.nameSearch"
                  :dense="true"
                  :email="bookingForm.email"
                  :mobile="bookingForm.mobile"
                  :selected="bookingForm.name"
                  background-color=""
                  class-name="q-text-field shadow-0"
                  hide-details="auto"
                  label=""
                  outlined
                  readonly
                  :venueServiceId="venue_service_id"
                  :productTypeId="6"
                  @updateCustomer="setCustomerData"
              >
              </v-name-search>
            </div>
          </v-col>

          <v-col md="6" sm="12">
            <div>
              <label for="">Email{{ field.email.is_required ? '*' : '' }}</label>
              <v-text-field
                  v-model="bookingForm.email"
                  :placeholder="`Email${field.email.is_required ? '*' : ''}`"
                  :readonly="bookingForm.customer_id != null"
                  :rules="emailRule"
                  background-color="#fff"
                  class="q-text-field shadow-0"
                  dense
                  label=""
                  outlined
                  validate-on-blur
                  hide-details="auto"
              ></v-text-field>
            </div>
          </v-col>
          <v-col md="6" sm="12" v-if="field.gender.is_visible || (field.dob.is_visible && customerAgeRange) || (field.dob.is_visible && !customerAgeRange)">
            <v-row class="mx-0">
              <v-col v-if="field.gender.is_visible" :md="field.dob.is_visible?6:12" sm="12" class="pl-0 pr-1">
                <label for="">Gender{{ field.gender.is_required ? '*' : '' }}</label>
                <v-select
                    v-model="bookingForm.gender"
                    :items="['Male', 'Female']"
                    :menu-props="{ bottom: true, offsetY: true }"
                    :placeholder="`Gender${field.gender.is_required ? '*' : ''}`"
                    :rules="genderRule"
                    background-color="#fff"
                    class="q-text-field shadow-0"
                    dense
                    label=""
                    outlined
                    validate-on-blur
                    hide-details="auto"
                ></v-select>
              </v-col>
              <v-col v-if="field.dob.is_visible && customerAgeRange"  :md="field.gender.is_visible?6:12" class="pr-0 pl-1" sm="12">
                <label for="">Age Group{{ field.dob.is_required ? '*' : '' }}</label>
                <v-select
                    v-if="customerAgeRange"
                    v-model="bookingForm.age_group"
                    :items="ageRanges"
                    :menu-props="{ bottom: true, offsetY: true }"
                    background-color="#fff"
                    class="q-text-field shadow-0"
                    dense
                    item-text="name"
                    item-value="id"
                    outlined
                    validate-on-blur
                    hide-details="auto"
                ></v-select>
              </v-col>
              <v-col v-if="field.dob.is_visible && !customerAgeRange"  :md="field.dob.is_visible?6:12" class="pr-0 pl-1" sm="12">
                <label for="">Date of Birth{{ field.dob.is_required ? '*' : '' }}</label>
                <date-of-birth
                    v-model="bookingForm.dob"
                    :dense="true"
                    :placeholder="`Date of Birth${field.dob.is_required ? '*' : ''}`"
                    :rules="dobRule()"
                    class-name="q-text-field shadow-0 add-on"
                    hide-details="auto"
                    label=""
                >
                </date-of-birth>
              </v-col>
            </v-row>
          </v-col>
          <v-col v-if="field.nationality.is_visible" md="6" sm="12">
            <label for="">Nationality{{ field.nationality.is_required ? '*' : '' }}</label>
            <v-autocomplete
                v-model="bookingForm.country_id"
                :hint="`Nationality${field.nationality.is_required ? '*' : ''}`"
                :items="countries"
                :rules="nationalityRule"
                background-color="#fff"
                class="q-text-field shadow-0"
                dense
                item-text="name"
                item-value="id"
                label=""
                outlined
                validate-on-blur
                hide-details="auto"
            ></v-autocomplete>
          </v-col>
          <v-col v-if="field.idProof.is_visible" md="6" sm="12">
            <label for="">ID Type{{ field.idProof.is_required ? '*' : '' }}</label>
            <v-select
                v-model="bookingForm.id_proof_type_id"
                :hint="`ID Type${field.idProof.is_required ? '*' : ''}`"
                :items="idProofTypes"
                :menu-props="{ bottom: true, offsetY: true }"
                :rules="idTypeRule"
                background-color="#fff"
                class="q-autocomplete shadow-0"
                dense
                item-text="name"
                item-value="id"
                label=""
                outlined
                validate-on-blur
                @change="changeIdProofTypeId"
                hide-details="auto"
            ></v-select>
          </v-col>
          <v-col v-if="field.idProof.is_visible" md="6" sm="12">
            <div class="mb-4">
              <label for="">ID Number{{ field.idProof.is_required ? '*' : '' }}</label>
              <v-text-field
                  :key="refreshIdProofNumber"
                  v-model="bookingForm.id_proof_number"
                  :hint="`ID Number${field.idProof.is_required ? '*' : ''}`"
                  :rules="idTypeRule"
                  background-color="#fff"
                  class="q-text-field shadow-0"
                  dense
                  outlined
                  validate-on-blur
                  hide-details="auto"
              ></v-text-field>
            </div>
            <div>
              <label>{{ bookingForm.id_proof_path ? 'Download' : 'ID Proof' }} {{
                  field.idProof.is_required ? '*' : ''
                }}</label>
              <div class="d-flex align-center gap-x-2">
              <v-file-input
                  v-model="bookingForm.id_proof"
                  :rules="idProofRule"
                  background-color="#fff"
                  class="q-text-field shadow-0 w-full"
                  dense
                  hide-details="auto"
                  outlined
                  prepend-icon=""
              >
                <template v-slot:prepend-inner>
                  <v-tooltip bottom>
                    <template v-slot:activator="{ on }">
                      <v-icon
                          v-if="bookingForm.id_proof_path"
                          color="cyan"
                          @click="openFile(bookingForm.id_proof_path)"
                          v-on="on"
                      >
                        mdi-download-box
                      </v-icon>
                      <AttachmentIcon v-else v-on="on"/>
                    </template>
                    <span v-if="bookingForm.id_proof_path"
                    >Download uploaded file</span
                    >
                    <span v-else>Upload ID Proof</span>
                  </v-tooltip>
                </template>
                <template v-slot:selection="{ index, text }">
                  <v-chip
                      v-if="index == 0"
                      style="background-color: #edf7f7"
                      dark
                      label
                      small
                  >
                  <span class="text-truncate text-black" style="max-width: 120px">{{
                      text
                    }}</span>
                  </v-chip>
                </template>
              </v-file-input>
                <v-btn
                    class="white--text q-text-field shadow-0 bordered px-0"
                    color="blue-grey"
                    height="40"
                    width="40"
                    max-width="40"
                    min-width="40"
                    large
                    outlined
                    style="background-color: #fff"
                    @click="idProofWebcamDialog = true"
                >
                  <CameraIcon/>
                </v-btn>
            </div>
            </div>
            <!-- <div style="margin-top: -110px"></div> -->
          </v-col>
          <v-col v-if="field.tag.is_visible" md="6" sm="12">
            <label for="">Tags{{ field.tag.is_required ? '*' : '' }}</label>
            <v-select
                v-model="bookingForm.customer_tag"
                :items="tags"
                :menu-props="{ bottom: true, offsetY: true }"
                :placeholder="`Tags${field.tag.is_required ? '*' : ''}`"
                :rules="tagRule"
                background-color="#fff"
                class="q-autocomplete shadow-0"
                dense
                item-text="name"
                item-value="id"
                label=""
                multiple
                outlined
                return-object
                validate-on-blur
                hide-details="auto"
            ></v-select>
          </v-col>
          <v-col v-if="field.image.is_visible" md="6" sm="12">
            <v-row>
              <v-col md="10">
                <label for="">
                  Profile Image {{ field.image.is_required ? '*' : '' }}
                </label>
                <v-file-input
                    v-model="bookingForm.profile_image"
                    :rules="imageRule"
                    background-color="#fff"
                    class="q-text-field shadow-0 w-full"
                    dense
                    hide-details="auto"
                    outlined
                    prepend-icon=""
                    show-size
                >
                  <template v-slot:prepend-inner>
                    <v-tooltip bottom>
                      <template v-slot:activator="{ on }">
                        <v-icon
                            v-if="bookingForm.image_path"
                            color="cyan"
                            @click="openFile(bookingForm.image_path)"
                            v-on="on"
                        >
                          mdi-download-box
                        </v-icon>
                        <ProfileUploadIcon v-else v-on="on"/>
                      </template>
                      <span v-if="bookingForm.image_path"> Download image</span>
                      <span v-else>Upload Image</span>
                    </v-tooltip>
                  </template>
                  <template v-slot:selection="{ index, text }">
                    <v-chip
                        v-if="index == 0"
                        style="background-color: #edf7f7"
                        dark
                        label
                        small
                    >
                  <span class="text-truncate text-black" style="width: 120px">{{
                      text
                    }}</span>
                    </v-chip>
                  </template>
                </v-file-input>
              </v-col>
              <v-col align-self="end" md="2" class="justify-center px-0">
                <v-btn
                    class="white--text q-text-field shadow-0 bordered"
                    color="blue-grey"
                    height="40"
                    width="40"
                    min-width="40"
                    large
                    outlined
                    style="background-color: #fff"
                    @click="webcamDialog = true"
                >
                  <CameraIcon/>
                </v-btn>
              </v-col>
            </v-row>
          </v-col>
          <additional-fields
              v-for="(field_config, index) in additionalFields"
              :key="index"
              :fieldConfig="field_config"
              v-model="bookingForm['additional_fields'][index]"
              :col="{md:6,sm:12}"
          />
        </v-row>
        <capture-image
            :open="webcamDialog"
            @close="webcamDialog = false"
            @confirm="confirmImageCapture"
        />
        <capture-image
            :open="idProofWebcamDialog"
            @close="idProofWebcamDialog = false"
            @confirm="confirmIdProofCapture"
        />
      </v-expansion-panel-content>
    </v-expansion-panel>

  </v-expansion-panels>
</template>
<script>
import moment, { now } from 'moment'
import VMemberSearch from '@/components/Customer/VMemberSearch'
import bookingFields from '@/mixins/bookingFieldValidation'
import CaptureImage from '@/components/Image/CaptureImage'
import EmirateIdReaderIcon from '@/assets/images/schedule/emirate-id-reader.svg'
import SamosoTechReaderIcon from '@/assets/images/schedule/samsotech-reader.svg'
import MinusIcon from '@/assets/images/misc/minus-icon.svg'
import PlusIcon from '@/assets/images/misc/plus-icon.svg'
import DeleteIcon from "@/assets/images/misc/delete-bg-icon.svg";
import CameraIcon from "@/assets/images/misc/camera-icon.svg";
import AttachmentIcon from "@/assets/images/misc/id-card-icon.svg";
import ProfileUploadIcon from "@/assets/images/misc/profile-upload-icon.svg";
import CustomSwitch from '@/components/Common/CustomSwitch.vue'
import AdditionalFields from "@/components/Schedule/Facility/AdditionalFields.vue";

export default {
  props: {
    perCapacity: { type: Number, default: 0 },
    singleBookingForm: { type: Object },
    promotions: { type: Array },
    countries: { type: Array },
    tags: { type: Array },
    companies: { type: Array },
    idProofTypes: { type: Array },
    id: { type: Number, default: 0 },
    venue_service_id: { type: Number },
    order_id: { type: Number },
    disablePromotion: { type: Boolean, default: false },
    addOn: { type: Number, default: 0 },
  },
  mixins: [bookingFields],
  components: {
    AdditionalFields,
    CustomSwitch,
    PlusIcon,
    MinusIcon,
    VMemberSearch,
    CaptureImage,
    EmirateIdReaderIcon,
    SamosoTechReaderIcon,
    DeleteIcon,
    CameraIcon,
    AttachmentIcon,
    ProfileUploadIcon
  },
  data () {
    return {
      webcamDialog: false,
      idProofWebcamDialog: false,
      refreshIdProofNumber: now(),
      openPanel: 0
    }
  },
  computed: {

    customerAgeRange () {
      return this.$store.getters.getCustomerAgeRangeConfiguration.data
    },
    ageRanges () {
      return this.$store.getters.getCustomerAgeRange.data
    },

    bookingForm () {
      if (this.singleBookingForm !== '') {
        return this.singleBookingForm
      } else {
        return ''
      }
    },
  },
  mounted () {
    this.setFieldConfigurations()
    if (this.$store.getters.getCustomerAgeRangeConfiguration.status == false) {
      this.$store.dispatch('LoadCustomerAgeRangeConfiguration')
    }
    if (this.$store.getters.getCustomerAgeRange.status == false) {
      this.$store.dispatch('LoadCustomerAgeRange')
    }

    console.log(`mountedFields`,this.additionalFields)

  },
  watch:{
    additionalFields(val){
      console.log(`watch`,val)
    }
  },
  methods: {
    setMemberData (data) {
      this.$emit('setMemberData', data)
    },
    setCustomerData (data) {
      console.log('setIOSAASAS',data)
      this.$emit('setCustomerData', data)
    },
    setCardData (data) {
      console.log('setIOSAASASaaaaa',data)
      this.$emit('setCustomerData', data)
    },
    customerTypeChange () {
      console.log('customerTypeChange')

      this.$emit('customerTypeChange')
    },
    verifyBenefit (type) {
      this.$emit('verifyBenefit', type)
    },
    clearBenefit () {
      this.$emit('clearBenefit')
    },
    getCompanySales () {
      return this.bookingForm.company_id != null && this.companies.length
          ? this.companies.find((item) => item.id == this.bookingForm.company_id)
              .company_sale
          : []
    },
    confirmImageCapture (image) {
      image.name = this.bookingForm.name
          ? this.bookingForm.name + '_' + moment().format('YYYYMMDDHHSS')
          : 'user_image_' + moment().format('YYYYMMDDHHSS')
      this.bookingForm.profile_image = image
      this.webcamDialog = false
    },
    confirmIdProofCapture (image) {
      image.name = this.bookingForm.name
          ? this.bookingForm.name + '_' + moment().format('YYYYMMDDHHSS')
          : 'id_proof_' + moment().format('YYYYMMDDHHSS')
      this.bookingForm.id_proof = image
      this.idProofWebcamDialog = false
    },
    changeIdProofTypeId () {
      if (
          this.bookingForm.customer_documents &&
          this.bookingForm.customer_documents.length
      )
      {
        let objType = this.bookingForm.customer_documents.find((x) => {
          return x.id_proof_type_id === this.bookingForm.id_proof_type_id
        })
        if (typeof objType !== 'undefined' && objType.id_proof_type_id) {
          this.bookingForm.id_proof_number = objType.id_proof_number
          this.bookingForm.id_proof_path = objType.id_proof_path
          this.refreshIdProofNumber = now()
        } else {
          this.bookingForm.id_proof_number = null
          this.bookingForm.id_proof_path = null
          this.refreshIdProofNumber = now()
        }
      } else {
        // console.log("document length 0");
      }
    },
    deleteCustomer () {
      console.log('emitting')
      this.$emit('removeCustomerAdd')
    },
  },
}
</script>
<style lang="scss">
.reader-button {
  max-width: 30px !important;
  min-width: 30px !important;
  width: 30px !important;
}
.odd-bordered-row{
  & > div{
    padding-top: 6px !important;
    padding-bottom: 6px !important;
  }
  & > div:nth-child(2n+1){
    border-right: 1px solid #F1F1F1;
  }
}
</style>
<!--  <customer-booking-form
                  :promotions="promotions"
                  :singleBookingForm="bookingForm"
                  :id="id"
                  :order_id="order_id"
                  :countries="countries"
                  :idProofTypes="idProofTypes"
                  @setCustomerData="(data) => setCustomerData(data)"
                  @setMemberData="(data) => setMemberData(data)"
                  @customerTypeChange="(data) => customerTypeChange(null, data)"
                  @clearBenefit="(data) => clearBenefit()"
                /> -->
