<template>
  <v-container fluid>
    <SeatMapTabs />
    <v-divider class="mt-1" style="border-color: rgba(17, 42, 70, 0.14) !important;"/>
    <div class="row row--dense mt-1">
      <div class="col-12">
       <span class="d-flex text-center align-center gap-x-2 " style="height: 20px ; justify-content: flex-end">
            <span class="">Active seatmap</span>:
            <v-switch v-model="seatMapStatus" false-value="inactive" true-value="active" @change="filterSeatMaps"></v-switch>
          </span>
      </div>
    </div>
    <v-row class="mt-1">
      <v-col v-for="(seatmap, i) in seatmaps" :key="i" lg="4" md="6" sm="12" xl="3">
        <SeatMapWidget v-bind="seatmap" @duplicate="duplicateSeatMapCreate" @delete="deleteSeatMap" @edit="redirectToSeatMapTool"/>
      </v-col>
    </v-row>
    <div class="row text-center">
      <div v-if="seatmaps.length == 0" class="col-12 text-center"><h2> No {{ seatMapStatus }} seatmap present</h2></div>
      <div class="col-12">
        <v-pagination v-if="totalPages" v-model="page" :length="totalPages" class="new-pagination"></v-pagination>
      </div>
    </div>
    <confirm-model
        v-bind="confirmModel"
        @confirm="confirm"
        @close="confirmModel.id = null"
    ></confirm-model>
  </v-container>
</template>
<script>
import SeatMapTabs from "@/views/SeatMap/SeatMapTab.vue";
import SeatMapWidget from "@/views/SeatMap/SeatMapWidget.vue";
export default {
  components: {
    SeatMapTabs,
    SeatMapWidget,
  },
  data() {
    return {
      seatMapStatus: 'active',
      seatmaps: [],
      page: 1,
      totalPages: 0,
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
    };
  },
  mounted() {
    this.filterSeatMaps();
  },
  watch: {
    page() {
      this.loadSeatMaps();
    },
  },
  methods: {
    loadSeatMaps() {
      let url = `&status=${this.seatMapStatus}&per_page=12`;
      this.showLoader("Loading Maps");
      this.$http
          .get(`venues/seat-maps/list?page=${this.page}${url}`)
          .then((response) => {
            if (response.status == 200 && response.data.status == true) {
              this.hideLoader();
              this.seatmaps = response.data.data;
              this.totalPages = response.data.total_pages;
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    filterSeatMaps() {
      if (this.page != 1) {
        this.page = 1;
      } else {
        this.loadSeatMaps();
      }
    },
    duplicateSeatMapCreate(mapId) {
      console.log("duplicate calling ",mapId);
      this.confirmModel = {
        id: mapId,
        title: `Do you want to duplicate of this seatmap?`,
        description: "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action?",
        type: "duplicate",
      };
    },
    confirm(data) {
      if (data.type === "duplicate") {
        this.duplicateSeatMapConfirm(data.id);
      }else if( data.type === "delete"){
        this.deleteSeatMapConfirm(data.data);
      }
      this.$forceUpdate();
      this.confirmModel.id = null;
    },
    duplicateSeatMapConfirm(mapId){
      this.showLoader("Duplicate Map");
      this.$http
          .post(`venues/seat-maps/duplicate`,{id: mapId})
          .then((response) => {
            if (response.status === 200 && response.data.status === true) {
              this.loadSeatMaps();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    deleteSeatMap(data){
      console.log("data",data)
      let action = data.status_id === 1?'deactivate':'reactivate';
      this.confirmModel = {
        id: data.id,
        title: `Do you want to ${action} this seatmap?`,
        description: "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action?",
        type: "delete",
        data: {
          id: data.id,
          type: action,
        },
      };
    },
    deleteSeatMapConfirm(data){
      this.$http
          .post(`venues/seat-maps/delete`,data)
          .then((response) => {
            if (response.status === 200 && response.data.status === true) {
              this.loadSeatMaps();
            }
          })
          .catch((error) => {
            this.errorChecker(error);
          });
    },
    redirectToSeatMapTool(mapId){
      if(! mapId){
        this.showError("Map id not found.");
        return;
      }
      let token = this.$store.getters.token;
      console.log("token",token);
      if (!token) {
        this.showError("No auth token found.");
        return;
      }
      if (token.startsWith("Bearer ")) {
        token = token.replace("Bearer ", "");
      }
      // Ensure the environment variable has the correct key, like VUE_APP_SEATMAP_URL
      const seatmapURL = process.env.VUE_APP_SEATMAP_URL;
      if (!seatmapURL) {
        this.showError("Seatmap tool URL is not defined.");
        return;
      }
      const redirectURL = `${seatmapURL}?token=${encodeURIComponent(token)}&login_url=${encodeURIComponent(window.location.origin)}&smId=${mapId}&source=qp`;
      window.location.href = redirectURL;
    }

  },
};
</script>
<style scoped>
</style>
