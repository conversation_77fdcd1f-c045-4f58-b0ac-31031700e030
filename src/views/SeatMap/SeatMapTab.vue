<script>
//import TrainerIcon from "@/assets/images/trainers/trainers_icon.svg";
// import LogsIcon from "@/assets/images/trainers/logs_icon.svg";
// import SvgIcon from "@/components/Image/SvgIcon.vue";
// import ConfigIcon from "@/assets/images/trainers/cog_icon.svg";
//import AddIcon from "@/assets/images/misc/plus-icon.svg";
export default {
  name: "SeatMapTab",
  components: {
    //AddIcon,
    //ConfigIcon, SvgIcon, LogsIcon,
   // TrainerIcon
  },
  computed: {
    tabs() {
      return [
        {
          name: "Dashboard",
          //icon: TrainerIcon,
          path: ["/seatmap"],
          permission: true
        },
      ]
    }
  },
  methods: {
    gotoPage(route) {
      this.$router.push(route);
    },
    redirectToSeatMapTool(){
      let token = this.$store.getters.token;
      console.log("token",token);
      if (!token) {
        this.showError("No auth token found.");
        return;
      }
      if (token.startsWith("Bearer ")) {
        token = token.replace("Bearer ", "");
      }
      // Ensure the environment variable has the correct key, like VUE_APP_SEATMAP_URL
      const seatmapURL = process.env.VUE_APP_SEATMAP_URL;
      if (!seatmapURL) {
        this.showError("Seatmap tool URL is not defined.");
        return;
      }
      const redirectURL = `${seatmapURL}?token=${encodeURIComponent(token)}&login_url=${encodeURIComponent(window.location.origin)}`;
      window.location.href = redirectURL;
   }
  }
}
</script>
<template>
  <v-row dense>
    <v-col cols="12" lg="12" xl="12">
      <div class="d-flex justify-space-between ">
        <div class="d-flex qp-tab-nav">
          <div class="d-flex align-center  tabs">
<!--            <template v-for="(tab,index) in tabs">-->
<!--              <div-->
<!--                  v-if="tab.permission"-->
<!--                  v-bind:key="tab.name"-->
<!--                  :class="{'tab':index < tabs.length-1}"-->
<!--                  class="nv_item d-flex pointer"-->
<!--                  @click="gotoPage(tab.path[0])"-->
<!--              >-->
<!--                <SvgIcon-->
<!--                    :class="{-->
<!--                    'qp-tab-nav-is-active':tab.path.includes($route.path),-->
<!--                    'qp-tab-nav':!tab.path.includes($route.path)-->
<!--                  }"-->
<!--                    :text="tab.name"-->
<!--                    class="text-thin-gray"-->
<!--                >-->
<!--                  <template v-slot:icon>-->
<!--                    <component :is="tab.icon" :component="tab.icon"/>-->
<!--                  </template>-->
<!--                </SvgIcon>-->
<!--              </div>-->
<!--            </template>-->
          </div>
        </div>
        <div class="d-flex gap-x-2">
          <v-btn
              v-if="checkWritePermission($modules.trainers.dashboard.slug)"
              class="white--text blue-color ml-1"
              color="darken-1"
              height="34"
              text
              @click="redirectToSeatMapTool"
          >
            <span class="ml-1">Creat seatmap</span>
          </v-btn>
        </div>
      </div>
    </v-col>
  </v-row>
</template>