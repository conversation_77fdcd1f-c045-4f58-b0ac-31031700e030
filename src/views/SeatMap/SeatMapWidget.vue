<template>
  <v-card style="border-radius: 8px" class="shadow map_card" @click="viewMap(id)">
    <v-card-text class="relative">
      <v-menu absolute content-class="q-menu" right top>
        <template v-slot:activator="{ on, attrs }">
          <v-btn
              :ripple="false"
              absolute
              class="text_capitalize "
              elevation="0"
              right
              style="background-color: transparent; min-width: fit-content !important; height: fit-content !important; padding: 2px !important; top:10px "
              top
              v-bind="attrs"
              v-on="on"
          >
            <DotsIcon/>
          </v-btn>
        </template>
        <v-list>
          <v-list-item v-if="checkWritePermission($modules.seatmap.management.slug)" @click="$emit('duplicate',id)">
            <SvgIcon text="Duplicate" class="font-medium text-sm gap-x-2">
              <template #icon>
                <CopyIcon/>
              </template>
            </SvgIcon>
          </v-list-item>
          <v-list-item
              v-if="checkWritePermission($modules.seatmap.management.slug) && (status_id == 1 || status_id == 11)"
              @click="$emit('edit',id)"
          >
            <SvgIcon class="font-medium text-sm gap-x-2" text="Edit">
              <template #icon>
                <EditIcon height="16" viewBox="0 0 20 20" width="16"/>
              </template>
            </SvgIcon>
          </v-list-item>
          <v-divider class="mb-2"/>
          <v-list-item v-if="checkDeletePermission($modules.seatmap.management.slug)" @click="$emit('delete',{
              id: id,
              status_id: status_id,
              flag: false,
            })">
            <SvgIcon :text="(status_id === 1 ? 'Deactivate' : 'Activate')"
                     class="font-medium text-sm gap-x-2" :class="{'red--text svg-stroke-red':status_id === 1}">
              <template #icon>
                <ActivateIcon/>
              </template>
            </SvgIcon>
          </v-list-item>
        </v-list>
      </v-menu>
      <v-row class="border-bottom pb-2" dense>
        <v-col md="5">
          <view-image
              :image="map_image"
              defaultImage="user"
              style="border-radius: 0.5rem"
              :height="130"
          ></view-image>
        </v-col>
        <v-col md="7">
          <div class="mt-4">
            <p class="font-semibold text-lg black--text text-elepsis" :title="map_name">{{ map_name }}</p>
            <div class="d-flex gap-x-2 align-center mt-5 stats_col">
              Total Seats:
              <span class="black--text text-base text-elepsis">{{total_seats}}</span>
            </div>
            <div class="d-flex gap-x-2 align-center mt-2 stats_col">
              Status:
              <span class="black--text text-base email_trainer">{{ status_id == 1?'Active':'Deactive' }}</span>
            </div>
          </div>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>
<script>
import CopyIcon from "@/assets/images/misc/copy-icon.svg";
import EditIcon from "@/assets/images/tables/edit.svg";
import SvgIcon from "@/components/Image/SvgIcon.vue";
import DotsIcon from "@/assets/images/misc/h-options.svg";
import ActivateIcon from "@/assets/images/partners/activate.svg";
export default {
  components: { CopyIcon,EditIcon,SvgIcon,DotsIcon,ActivateIcon },
  props: {
    id: { type: Number, default: 0 },
    map_name: { type: String, default: "" },
    total_seats: { type: Number, default: 0 },
    bookings: { type: Number, default: 0 },
    status_id: { type: Number, default: 1 },
    map_image: {
      type: String,
      default: null,
    },
  },
  methods: {
    viewMap(id) {
      console.log("view map",id);
    },
  },
  data() {
    return {};
  },
  mounted() {},
  computed: {},
};
</script>
<style scoped>
.map_card {
  border: 1px solid rgba(17, 42, 70, 0);
  cursor: pointer;
}
.text-elepsis {
  white-space: nowrap; /* Prevents the text from wrapping */
  text-overflow: ellipsis; /* Adds ellipsis (...) to indicate text overflow */
  max-width: 85%;
  overflow: hidden;
}
.map_card:hover {
  border: 1px solid rgba(17, 42, 70, 1);
}
</style>
