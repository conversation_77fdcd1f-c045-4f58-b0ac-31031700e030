  <template>
    <div>
      <v-dialog v-model="showEventBookingSeatMapForm"  max-width="780px"   @input="$emit('close')" scrollable persistent>
        <v-card class="px-0 pt-0">
          <v-card-text>
            <div class="pt-4 pb-0 d-flex justify-space-between align-center border-bottom">
              <v-row class="px-0 py-0 m-0" >
                <v-col cols="8">
                  <h3 class="font-semibold text-blue pb-2 pt-2">Customer Details 00</h3>
                </v-col>
                <v-col cols="4">
                  <div v-if="promotions.length > 0" class="promotion-dd">
                    <v-autocomplete
                        v-if="bookingForm.card_number == null"
                        :items="[ { name: 'Promotion', promotion_code: null }, ...promotions,]"
                        item-text="name"
                        label=""
                        item-value="promotion_code"
                        v-model="bookingForm.promotion_code"
                        outlined
                        @change="verifyBenefit('promotion')"
                        dense
                        hide-details="auto"
                        class="q-autocomplete shadow-0"
                    >
                    </v-autocomplete>
                  </div>
                </v-col>
              </v-row>
              <div class="pointer" @click="$emit('close')">
                <ModalCloseIcon/>
              </div>
            </div>
            <v-form ref="form" v-model="valid">
              <v-expansion-panels v-model="openPanel[index]" class="border-0 my-5" v-for="(customer, index) in event_customers" :key="index">
                <v-expansion-panel>
                  <v-expansion-panel-header class="p-0" hide-actions style="min-height: 44px !important;">
                  <template v-slot:default="{ open }">
                    <div :class="{'py-1':open,'py-3':!open}" class="d-flex justify-space-between align-center bg-neon opacity-10 px-4 w-full">
                      <div class="title-text">{{ index == 0?'Primary': 'Additional' }} Customer</div>
                      <div class="d-flex align-center gap-x-2">
                        <card-data-button
                            v-if="open"
                            :outlined="false"
                            :plain="true"
                            class-name="blue-text px-0 reader-button"
                            label="HID Omnikey"
                            @data="(data) => { setCardData(data); }"
                        >
                          <EmirateIdReaderIcon/>
                        </card-data-button>
                        <card-reader-button
                            v-if="open"
                            :outlined="false"
                            :plain="true"
                            className="blue-text px-0 reader-button"
                            label="Samsotech Reader"
                            @data="
                              (data) => {
                                setCardData(data);
                              }
                            "
                        >
                          <SamosoTechReaderIcon/>
                        </card-reader-button>
                        <MinusIcon v-if="open"/>
                        <PlusIcon v-else/>
                      </div>
                    </div>
                  </template>

                </v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <v-tooltip v-if="index > 0" bottom>
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn
                            absolute
                            fab
                            top
                            v-bind="attrs"
                            x-small
                            elevation="0"
                            style="right: 0"
                            @click="deleteCustomer(index)"
                            v-on="on"
                        >
                          <DeleteIcon/>
                        </v-btn>
                      </template>
                      Delete
                    </v-tooltip>
                    <v-row  class="px-4 pt-4 pb-4 mx-0">
                      <v-col md="12" sm="12" class="m-0 py-0" >
                        <v-row class="">
                          <v-col md="8" lg="8" class="py-2">
                            <div class="top-member-radio-div" v-if="index == 0" >
                              <div class="member-radio" v-if="index == 0">
                                <v-radio-group
                                    v-model="customer.customer_type"
                                    class-name="custom-radio-group shadow-0 mt-0 mr-1"
                                    row
                                    @change="customerTypeChange"
                                    mandatory
                                    :dense="true"
                                    hide-details="auto"
                                    outlined
                                >
                                  <v-radio label="Normal" class="custom-radio" color="cyan" value="normal"></v-radio>
                                  <v-radio label="Member" class="custom-radio" color="cyan" value="member"></v-radio>
                                </v-radio-group>
                              </div>
                              <div class="member-card-div mt-2" v-if="customer.customer_type == 'member' && index == 0">
                                <v-member-search
                                    v-model="customer.member"
                                    @clear="clearBenefit"
                                    :selected="customer.card_number"
                                    @updateCustomer="(data) => setMemberData(data,index)"
                                    class-name="q-text-field shadow-0"
                                    :dense="true"
                                    :showLabel="false"
                                    hide-details="auto"
                                    outlined
                                >
                                </v-member-search>
                              </div>
                            </div>
                          </v-col>
                          <v-col md="4" lg="4" class="py-2">
                            <div class="d-flex justify-end align-center py-2" md="4">
                              <CustomSwitch
                                  v-model="customer.opt_marketing"
                                  :model-value="customer.opt_marketing"
                                  :show-label="true"
                                  class-name="d-flex flex-row-reverse"
                                  label="Opt In Marketing"
                                  @update:modelValue="(val)=>{
                                  customer.opt_marketing=val;
                                }"
                              />
                            </div>
                          </v-col>
                        </v-row>
                      </v-col>
                      <v-col md="6" sm="12">
                        <label for="">
                          Mobile No*
                        </label>
                        <v-mobile-search
                            v-model="customer.search"
                            :dense="true"
                            :selected="customer.mobile"
                            background-color=""
                            class-name="q-text-field shadow-0"
                            hide-details="auto"
                            outlined
                            readonly
                            @updateCustomer="setCustomerData($event, index)"
                            :showLabel="false"
                        ></v-mobile-search>
                      </v-col>
                      <v-col md="6" sm="12">
                        <label for="">
                          Name*
                        </label>
                        <v-name-search
                            v-model="customer.nameSearch"
                            :dense="true"
                            :email="customer.email"
                            :mobile="customer.mobile"
                            :selected="customer.name"
                            @updateCustomer="setCustomerData($event,index)"
                            background-color=""
                            class-name="q-text-field shadow-0"
                            hide-details="auto"
                            outlined
                            readonly
                            label=""
                        >
                        </v-name-search>
                      </v-col>
                      <v-col md="6" sm="12" >
                        <label for="">
                          Email{{ field.email.is_required ? '*' : '' }}
                        </label>
                        <v-text-field
                            v-model="customer.email"
                            :dense="true"
                            :placeholder="`Email${field.email.is_required ? '*' : ''}`"
                            :readonly="customer.customer_id != null"
                            class="q-text-field shadow-0"
                            hide-details="auto"
                            outlined
                            required
                            :rules="emailRule"
                        ></v-text-field>
                      </v-col>
                      <v-col v-if="field.gender.is_visible" md="6" sm="12">
                        <label for="">Gender{{ field.gender.is_required ? '*' : '' }}</label>
                        <v-radio-group
                            v-model="customer.gender"
                            class="mt-0 gender-radio"
                            :rules="genderRule"
                            hide-details="auto"
                            row
                        >
                          <v-radio label="Male" value="Male"></v-radio>
                          <v-radio label="Female" value="Female"></v-radio>
                        </v-radio-group>
                      </v-col>
                      <v-col v-if="field.dob.is_visible && customerAgeRange" md="6" sm="12">
                        <label for="">
                          Age Group{{ field.dob.is_required ? '*' : '' }}
                        </label>
                        <v-select
                            v-if="customerAgeRange"
                            v-model="customer.age_group"
                            :dense="true"
                            :items="[]"
                            :menu-props="{ bottom: true, offsetY: true }"
                            class-name="q-text-field shadow-0 "
                            hide-details="auto"
                            item-text="name"
                            item-value="id"
                            outlined
                        ></v-select>
                      </v-col>
                      <v-col v-if="field.dob.is_visible && !customerAgeRange" md="6" sm="12">
                        <label for="">
                          Date of Birth{{ field.dob.is_required ? '*' : '' }}
                        </label>
                        <date-of-birth
                            v-model="customer.dob"
                            :rules="dobRule()"
                            :dense="true"
                            class-name="q-text-field shadow-0"
                            outlined
                            hide-details="auto"
                        >
                        </date-of-birth>
                      </v-col>
                      <v-col v-if="field.nationality.is_visible" md="6" sm="12">
                        <label for="">
                          Nationality{{ field.nationality.is_required ? '*' : '' }}
                        </label>
                        <v-autocomplete
                            v-model="customer.country_id"
                            :items="countries"
                            background-color="#fff"
                            dense
                            :rules="nationalityRule"
                            hide-details="auto"
                            item-text="name"
                            item-value="id"
                            outlined
                            class="q-autocomplete shadow-0"
                        ></v-autocomplete>
                      </v-col>
                      <v-col md="6" sm="6" v-if="field.idProof.is_visible">
                        <label for="">
                          ID Type{{ field.idProof.is_required ? '*' : '' }}
                        </label>
                        <v-select
                            v-model="customer.id_proof_type_id"
                            :rules="idTypeRule"
                            :dense="true"
                            :menu-props="{ bottom: true, offsetY: true }"
                            background-color="#fff"
                            class="q-autocomplete shadow-0"
                            hide-details="auto"
                            outlined
                            item-value="id"
                            item-text="name"
                            :items="idProofTypes"
                            @change="changeIdProofTypeId($event, index)"
                        ></v-select>
                      </v-col>
                      <v-col sm="6" md="6" v-if="field.idProof.is_visible">
                        <v-row no-gutters>
                          <v-col md="7" >
                            <label for="">
                              ID Number{{ field.idProof.is_required ? '*' : '' }}
                            </label>
                            <v-text-field
                                :rules="idTypeRule"
                                outlined
                                v-model="customer.id_proof_number"
                                background-color="#fff"
                                class="text_field1 q-autocomplete shadow-0"
                                hide-details="auto"
                                :dense="true"
                            ></v-text-field>
                          </v-col>
                          <v-col md="5">
                            <label for="">
                              ID Image{{ field.idProof.is_required ? '*' : '' }}
                            </label>
                            <v-file-input
                                v-model="customer.id_proof"
                                :placeholder="`Select image${ field.image.is_required ? '*' : '' }`"
                                :label="`ID Proof${ field.image.is_required ? '*' : '' }`"
                                :rules="idProofRule"
                                prepend-icon=""
                                prepend-inner-icon="mdi-card-account-details"
                                background-color="#fff"
                                outlined
                                class="text_field2 q-autocomplete shadow-0"
                                hide-details="auto"
                                :dense="true"
                            >
                              <template v-slot:prepend-inner>
                                <v-tooltip bottom>
                                  <template v-slot:activator="{ on }">
                                    <v-icon
                                        color="cyan"
                                        v-if="customer.id_proof_path"
                                        @click="openFile(customer.id_proof_path)"
                                        v-on="on"
                                    >
                                      mdi-download-box
                                    </v-icon>
                                    <v-icon v-else v-on="on">
                                      mdi-card-account-details
                                    </v-icon>
                                  </template>
                                  <span v-if="customer.id_proof_path">Download uploaded file</span>
                                  <span v-else>Upload ID Proof</span>
                                </v-tooltip>
                              </template>
                              <template v-slot:selection="{ index, text }">
                                <v-chip v-if="index == 0" color="cyan accent-4" dark label small>
                                  <span style="width: 40px" class="text-truncate">{{ text }}</span>
                                </v-chip>
                              </template>
                            </v-file-input>
                          </v-col>
                        </v-row>
                        <div style="margin-top: -110px"></div>
                      </v-col>
                      <v-col sm="6" md="6" v-if="field.image.is_visible">
                        <v-row no-gutters>
                          <v-col md="8">
                            <label for="">
                              Profile Image{{ field.image.is_required ? '*' : '' }}
                            </label>
                            <v-file-input
                                v-model="customer.image"
                                prepend-icon=""
                                :label="`Customer Image${ field.image.is_required ? '*' : '' }`"
                                :placeholder="`Image${ field.image.is_required ? '*' : '' }`"
                                :rules="imageRule"
                                prepend-inner-icon="mdi-image"
                                background-color="#fff"
                                outlined
                                show-size
                                class="text_field1 q-autocomplete shadow-0"
                                hide-details="auto"
                                :dense="true"
                            >
                              <template v-slot:selection="{ index, text }">
                                <v-chip v-if="index == 0" color="cyan accent-4" dark label small>
                                  <span style="width: 120px" class="text-truncate">{{ text }}</span>
                                </v-chip>
                              </template>
                            </v-file-input>
                          </v-col>
                          <v-col md="4">
                            <label for="">
                             Capture Image
                            </label>
                            <v-btn large block style="background-color: #fff;border-color: #ccc;height: 40px;" outlined color="blue-grey" class="white--text text_field2" @click="setWebCamDialog(index)">
                              <v-icon dark>mdi-camera</v-icon>
                            </v-btn>
                          </v-col>
                        </v-row>
                      </v-col>
                      <v-col v-if="field.tag.is_visible" md="6" sm="12">
                        <label for="">
                          Tags{{ field.tag.is_required ? '*' : '' }}
                        </label>
                        <v-select
                            v-model="customer.customer_tag"
                            :rules="tagRule"
                            :items="tags"
                            :menu-props="{ bottom: true, offsetY: true }"
                            :placeholder="`Tags${field.tag.is_required ? '*' : ''}`"
                            background-color="#fff"
                            dense
                            hide-details="auto"
                            item-text="name"
                            item-value="id"
                            multiple
                            outlined
                            return-object
                            class="q-autocomplete shadow-0"
                        ></v-select>
                      </v-col>
                      <additional-fields
                          v-for="(field_config, index) in additionalFields"
                          :key="index"
                          :fieldConfig="field_config"
                          v-model="customer['additional_fields'][index]"
                          :col="{md:4,sm:4}"
                      />
                    </v-row>
                  </v-expansion-panel-content>
                </v-expansion-panel>
              </v-expansion-panels>
              <div class="add_btn mt-2 mb-2" style="margin-top: -30px" v-if="displayedParticipant < maxParticipants">
                <v-tooltip bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn v-bind="attrs" v-on="on" color="teal" fab x-small dark @click="addCustomer()">
                      <v-icon small>mdi-plus-circle</v-icon>
                    </v-btn>
                  </template>
                  Add
                </v-tooltip>
              </div>
              <v-divider />
            </v-form>

          </v-card-text>
          <v-card-actions>
            <span class="text-decoration-line-through" v-if="bookingForm.discount">{{ bookingForm.discount.actual_total | toCurrency }}</span>
            <span class="total-amount ma-2">AED {{ getTotalAmount }}</span>
            <v-spacer></v-spacer>
            <v-btn @click="close()" class="ma-2" text>Close</v-btn>
            <v-btn class="ma-2 white--text teal-color" @click="confirmBooking" text>Confirm Booking</v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
      <capture-image
          :open="webcamDialog"
          @close="webcamDialog = false"
          @confirm="confirmImageCapture"
      />
      <confirm-model
          v-bind="confirmModel"
          @confirm="confirmActions"
          @close="confirmModel.id = null"
      ></confirm-model>
    </div>
  </template>
  <script>
  import VMemberSearch from "@/components/Customer/VMemberSearch";
  import CaptureImage from "@/components/Image/CaptureImage";
  import moment from "moment";
  import bookingFields from "@/mixins/bookingFieldValidation";
  import ModalCloseIcon from "@/assets/images/misc/modal-close.svg";
  import EmirateIdReaderIcon from '@/assets/images/schedule/emirate-id-reader.svg';
  import SamosoTechReaderIcon from '@/assets/images/schedule/samsotech-reader.svg';
  import MinusIcon from '@/assets/images/misc/minus-icon.svg';
  import PlusIcon from '@/assets/images/misc/plus-icon.svg';
  import CustomSwitch from '@/components/Common/CustomSwitch.vue';
  import DeleteIcon from "@/assets/images/misc/delete-bg-icon.svg";
  import AdditionalFields from "@/components/Schedule/Facility/AdditionalFields.vue";
  export default {
    props: {
      showEventBookingSeatMapForm: { type: Boolean },
      selectedProducts: { type: Array, default: () => [] },
      maxParticipants: { type: Number, default: 0 },
      eventSeatMapId: {type: Number, default: 0},
      eventId: { type: Number,default: 0 },
      date: { type: String },
      customerAgeRange: {
        type: Boolean,
        default: false
      },
    },
    components: {
      AdditionalFields,
      CaptureImage,
      VMemberSearch,
      ModalCloseIcon,
      EmirateIdReaderIcon,
      SamosoTechReaderIcon,
      MinusIcon,
      PlusIcon,
      CustomSwitch,
      DeleteIcon

    },
    mixins: [bookingFields],
    data() {
      return {
        openPanel: [0],
        bookingForm: { price: 0 },
        event_customers: [
          {
            customer_type: null,
            member: null,
            company_id: null,
            company_sale_id: null,
            opt_marketing: null,
            mobile: null,
            name: null,
            email: null,
            nameSearch: null,
            customer_id: null,
            gender: null,
            country_id: null,
            id_proof_type_id: null,
            id_proof_number: null,
            id_proof: null,
            id_proof_path: null,
            image: null,
          },
        ],
        webcamDialog: false,
        valid: false,
        confirmModel: {
          id: null,
          title: null,
          description: null,
        },
        isEmiratesIdCheck: false,
        displayedParticipant: 1,
        currentCustomerIndex: 0,
      };
    },
    watch: {
      showEventBookingSeatMapForm(val) {
        if (val === true) {
          this.bookingForm = { price: 0, products: [...this.selectedProducts] };

          this.event_customers = [
            {
              customer_type: null,
              member: null,
              company_id: null,
              company_sale_id: null,
              opt_marketing: null,
              mobile: null,
              name: null,
              email: null,
              nameSearch: null,
              customer_id: null,
              gender: null,
              country_id: null,
              id_proof_type_id: null,
              id_proof_number: null,
              id_proof: null,
              id_proof_path: null,
              image: null,
            },
          ];
          this.$store.dispatch("loadPromotions", {
            date: this.date,
            product_type: "Event",
          });
          if (this.$refs.form) {
            this.$refs.form.resetValidation();
          }
        }
      },
    },
    mounted() {
      this.checkPermission = this.checkExportPermission(this.$modules.salesTeam.dashboard.slug);
      if (this.checkPermission) {
        this.$store.dispatch("loadSalesTeams", "Event");
        this.$forceUpdate();
      }
      if (this.$store.getters.getCountries.status == false) {
        this.$store.dispatch("loadCountries");
      }
      if (this.$store.getters.getIdProofTypes.status == false) {
        this.$store.dispatch("loadIDProofTypes");
      }
      if (this.$store.getters.getPaymentMethods.status == false) {
        this.$store.dispatch("loadPaymentMethods", "normal");
      }
      if (this.$store.getters.getTags.status == false) {
        this.$store.dispatch("loadTags");
      }
      if(this.selectedProducts && this.selectedProducts.length){
        console.log("selectedproducts",this.selectedProducts);
        this.bookingForm.products = [...this.selectedProducts];
      }
    },
    computed: {
      tags() {
        return this.$store.getters.getTags.data;
      },
      countries() {
        return this.$store.getters.getCountries.data;
      },
      promotions() {
        return this.$store.getters.getPromotions.data;
      },
      idProofTypes() {
        return this.$store.getters.getIdProofTypes.data.filter(i => !['Scanned document','Unified ID'].includes(i.name));
      },
      salesTeams() {
        return this.$store.getters.getSalesTeams.data;
      },
      getTotalAmount(){
         let totalAmount = 0;
        if(this.bookingForm.products && this.bookingForm.products.length > 0){
          this.bookingForm.products.forEach( (product) => {
            totalAmount += Number(product.total_price);
          });
        }
        return totalAmount;
      },
    },
    methods: {
      close() {
        this.event_customers = [];
        this.displayedParticipant = 1;
        this.bookingForm = null;
        this.bookingForm = { price: 0 };
        this.$emit("close");
      },
      setWebCamDialog(index) {
        this.currentCustomerIndex = index;
        this.webcamDialog = true;
      },
      setCardData(data, index) {
        this.setCustomerData(data, index);
      },
      setCustomerData(data, index = 0,isMemberCheck=false) {
        if (data.isEmiratesIdCheck) {
          this.isEmiratesIdCheck = true;
        }
        //   console.log(data);
        if (data.mobile && data.first_name && data.customer_id) {
          this.isEmiratesIdCheck = false;
          if(index === 0){
            let cn = null;
            if(isMemberCheck){
              cn = data.card_number;
            }
            this.searchMember(data.mobile, data.customer_id, data.first_name, data.last_name, index, cn);
          }
        }else{
          if (index === 0) {
            this.event_customers[0].member = null;
            this.event_customers[0].customer_type = 'normal';
            this.$set(this.event_customers[0], "card_number", null);
            if (this.bookingForm && this.bookingForm.promotion_code == null) {
              this.clearBenefit();
            }
          }
        }
        if (!data.customer_id) {
          this.$set(this.event_customers[index], "customer_id", null);
        }
        if (!data.name && data.first_name) {
          this.$set(this.event_customers[index], "name", data.first_name);
        }
        if (this.event_customers[index].customer_id && !data.customer_id && this.event_customers[index].name != data.name && this.event_customers[index].mobile != data.mobile) {
          this.$set(this.event_customers[index], "mobile", null);
          this.event_customers[index].search = null;
          this.event_customers[index].nameSearch = null;
          this.$set(this.event_customers[index], "email", null);
          this.$set(this.event_customers[index], "gender", null);
          this.$set(this.event_customers[index], "name", null);
          this.$set(this.event_customers[index], "customer_id", null);
          this.$set(this.event_customers[index], "first_name", null);
          this.$set(this.event_customers[index], "image_path", null);
          this.$set(this.event_customers[index], "dob", null);
          this.$set(this.event_customers[index], "country_id", null);
          this.$set(this.event_customers[index], "last_name", null);
          this.$set(this.event_customers[index], "opt_marketing", false);
          this.$set(this.event_customers[index], "id_proof_type_id", null);
          this.$set(this.event_customers[index], "id_proof_number", null);
          this.$set(this.bookingForm, "customer_tag", null);
          this.$set(this.event_customers[index], "id_proof_path", null);
          this.$forceUpdate();
        }
        if (data.mobile)
          this.$set(this.event_customers[index], "mobile", data.mobile);
        if (data.email)
          this.$set(this.event_customers[index], "email", data.email);
        if (data.country_id) {
          this.$set(this.event_customers[index], "country_id", data.country_id);
        } else {
          if (!this.isEmiratesIdCheck) {
            this.$set(this.event_customers[index], "country_id", null);
          }
        }
        if (data.customer_tag) {
          this.$set(this.event_customers[index], "customer_tag", data.customer_tag);
        } else {
          this.$set(this.event_customers[index], "customer_tag", null);
        }
        if (data.gender) {
          this.$set(this.event_customers[index], "gender", data.gender);
        } else {
          if (!this.isEmiratesIdCheck) {
            this.$set(this.event_customers[index], "gender", null);
          }
        }
        if (data.dob) {
          this.$set(this.event_customers[index], "dob", data.dob);
        } else {
          if (!this.isEmiratesIdCheck) {
            this.$set(this.event_customers[index], "dob", null);
          }
        }
        if (data.name) this.$set(this.event_customers[index], "name", data.name);
        if (data.last_name) {
          this.$set(this.event_customers[index], "last_name", data.last_name);
        } else {
          this.$set(this.event_customers[index], "last_name", null);
        }
        if (data.first_name)
          this.$set(this.event_customers[index], "first_name", data.first_name);
        if (data.customer_id)
          this.$set(this.event_customers[index], "customer_id", data.customer_id);
        if (data.image_path) {
          this.$set(this.event_customers[index], "image_path", data.image_path);
        } else {
          this.$set(this.event_customers[index], "image_path", null);
        }
        if (data.id_proof_type_id) {
          this.$set(this.event_customers[index], "id_proof_type_id", data.id_proof_type_id);
        }
        if (data.id_proof_path) {
          this.$set(this.event_customers[index], "id_proof_path", data.id_proof_path);
        } else {
          if (!this.isEmiratesIdCheck && data.customer_id) {
            this.$set(this.event_customers[index], "id_proof_path", null);
          }
        }
        if (data.id_proof) {
          this.$set(this.event_customers[index], "id_proof", data.id_proof);
        } else {
          if (!this.isEmiratesIdCheck && data.customer_id) {
            this.$set(this.event_customers[index], "id_proof", null);
          }
        }
        if (data.id_proof_number) {
          this.$set(this.event_customers[index], "id_proof_number", data.id_proof_number);
        }
        if (data.opt_marketing) {
          if (data.opt_marketing == 1) {
            this.$set(this.event_customers[index], "opt_marketing", true);
          } else {
            this.$set(this.event_customers[index], "opt_marketing", false);
          }
        }
        if (data.customer_documents) {
          this.event_customers[index].customer_documents = data.customer_documents;
          if (data.customer_documents[0] && data.customer_documents[0].id_proof_type_id) {
            this.$set(this.event_customers[index], "id_proof_type_id", data.customer_documents[0].id_proof_type_id);
          }
          if (data.customer_documents[0] && data.customer_documents[0].id_proof_number) {
            this.$set(this.event_customers[index], "id_proof_number", data.customer_documents[0].id_proof_number);
          }
          if (data.customer_documents[0] && data.customer_documents[0].id_proof_path) {
            this.$set(this.event_customers[index], "id_proof_path", data.customer_documents[0].id_proof_path);
          }
        } else {
          if (data.customer_id) {
            this.event_customers[index].customer_documents = [];
          }
        }
        this.$forceUpdate();
      },
      setMemberData(data,index) {
        if(index !== 0){
          return;
        }
        this.setCustomerData(data, index,true);
        if (index === 0) {
          this.$set(this.bookingForm, "card_number", data.card_number);
          this.bookingForm.customer_type = "member";
          this.$set(this.event_customers[index], "card_number", data.card_number);
          this.$set(this.event_customers[index], "membership_id", data.membership_id);
          this.verifyBenefit("membership", index);
        }
      },
      confirmImageCapture(image) {
        image.name = this.bookingForm.name ? this.bookingForm.name + "_" + moment().format("YYYYMMDDHHSS") : "user_image_" + moment().format("YYYYMMDDHHSS");
        this.event_customers[this.currentCustomerIndex].image = image;
        this.webcamDialog = false;
      },
      addCustomer() {
        if (this.displayedParticipant < this.maxParticipants) {
          this.event_customers.push({
            customer_type: "normal",
            member: null,
            company_id: null,
            company_sale_id: null,
            opt_marketing: null,
            mobile: null,
            name: null,
            email: null,
            nameSearch: null,
            customer_id: null,
            gender: null,
            country_id: null,
            id_proof_type_id: null,
            id_proof_number: null,
            id_proof: null,
            id_proof_path: null,
            image: null,
            additional_fields:[],
          });
          this.displayedParticipant++;
          this.$forceUpdate();
        }
      },
      verifyBenefit(type,index = 0) {
        if (this.bookingForm.products && this.bookingForm.products.length === 0) {
          this.showError("Please add at least one product.");
          return;
        }
        this.clearBenefit(index);
        let data = {
          products: [],
        };
        if (type === "promotion") {
          data.promotion_code = this.bookingForm.promotion_code;
          if (data.promotion_code == null) {
            this.clearBenefit();
            return;
          }else{
            if (this.bookingForm.card_number) {
              this.bookingForm.card_number = null;
            }
          }
        } else {
          if (this.bookingForm.card_number) {
            data.card_number = this.bookingForm.card_number;
          } else if (this.event_customers[0] && this.event_customers[0].card_number) {
            data.card_number = this.event_customers[0].card_number;
          }
        }
        if (this.bookingForm.mobile) {
          data.mobile = this.bookingForm.mobile;
        }
        if (this.bookingForm.discount) {
          data.products = [];
          this.bookingForm.products.forEach((product) => {
            let pdata = product;
            if (product.discount) {
              pdata.price = product.discount.actual_price;
              delete pdata.discount;
            }
            pdata.tax = pdata.tax_amount ?? pdata.tax;
            pdata.price = pdata.product_price;
            data.products.push(pdata);
          });
        } else {
          data.products = this.bookingForm.products;
          data.products.forEach((element) => {
            if (element) {
              // element.price = element.price / element.quantity;
              element.tax = element.tax_amount ?? element.tax;
            }
          });
        }
        let url = "venues/benefits/verify";
        this.$http
            .post(url, data)
            .then((response) => {
              if (response.status == 200 && response.data.status == true) {
                const newData = response.data.data;
                if(newData.discount){
                  this.bookingForm.discount = newData.discount?newData.discount:null;
                  this.bookingForm.price = newData.price;
                  this.bookingForm.products = newData.products;
                }else{
                  this.clearBenefit();
                }
                this.$forceUpdate();
              }
            })
            .catch((error) => {
              this.errorChecker(error);
            });
      },
      clearBenefit(index = 0) {
        if (this.bookingForm && this.bookingForm.discount && index === 0) {
          this.bookingForm.card_number = null;
          if (this.event_customers[index].customer_type !== "member") {
            this.event_customers[index].card_number = null;
          }
          if(this.event_customers[index].promotion_code){
            this.event_customers[index].promotion_code = null;
          }
          this.bookingForm.price = this.bookingForm.discount.actual_price;
          this.bookingForm.total_price = this.bookingForm.discount.actual_total;
          this.bookingForm.products = [...this.selectedProducts];
          setTimeout(() => {
            if(this.bookingForm.promotion_code){
              this.bookingForm.promotion_code = null;
            }
            this.bookingForm.discount = null;
            this.$forceUpdate();
          });
        }
      },
      customerTypeChange(e, index = 0) {
        console.log("index",index);
        if (index === 0) {
          if (this.event_customers[index].customer_type === "normal" && this.event_customers[index].card_number != null) {
            this.$set(this.event_customers[index], "card_number", null);
            this.clearBenefit(index);
          }
        }
      },
      confirmActions(data) {
        if (data.type == "customer") {
          this.event_customers.splice(data.id, 1);
          this.displayedParticipant--;
        }
        this.confirmModel.id = null;
        this.$forceUpdate();
      },
      deleteCustomer(index) {
        this.confirmModel = {
          id: index,
          title: "Do you want to remove this customer?",
          description:
              "By clicking <b>Yes</b> you can confirm the operation.  Do you need to continue your action ?",
          type: "customer",
        };
        this.$forceUpdate();
      },
      confirmBooking() {
        if (!this.$refs.form.validate()) {
          this.showError("Please fill all required fields");
          return;
        }
        this.showLoader("wait");
        var formData = new FormData();
        for (let key in this.bookingForm) {
          if (this.bookingForm[key] != null && !["id", "products", "search", "member", "discount", "customer_type", "card_number",].includes(key)) {
            if (key === "first_name" || key === "last_name") {
              if (!this.bookingForm["first_name"]) {
                formData.append(`first_name`, this.bookingForm["last_name"]);
              } else {
                formData.append(`first_name`, this.bookingForm["first_name"]);
                if (this.bookingForm["last_name"] && this.bookingForm["last_name"] != null) {
                  formData.append(`last_name`, this.bookingForm["last_name"]);
                }
              }
            } else {
              formData.append(`${key}`, this.bookingForm[key]);
            }
          }
        }
        this.event_customers.forEach((element, index) => {
          if (index === 0) {
            if (element["card_number"]) {
              formData.append(`card_number`, element["card_number"]);
            }
          }
          for (let key in element) {
            if (key !== "discount" && key !== "customer_tag" && element[key] != null && key !== "search" && key !== "nameSearch" && key !== "member") {
              formData.append(`customer[${index}][${key}]`, element[key]);
            } else if (key === "customer_tag" && element[key] != null) {
              element[key].forEach((tag, tagKey) => {
                formData.append(`customer[${index}][${key}][${tagKey}][id]`, tag.id);
                formData.append(`customer[${index}][${key}][${tagKey}][name]`, tag.name);
              });
            }
          }
        });
        this.bookingForm.products.forEach((element, index) => {
          for (let key in element) {
            if (key !== "discount" && key !== "seats") {
              formData.append(`products[${index}][${key}]`, element[key]);
            }
            if (key === "seats") {
              element[key].forEach((seat, seatKey) => {
                formData.append(`products[${index}][${key}][${seatKey}][seat_id]`, seat.id);
                formData.append(`products[${index}][${key}][${seatKey}][seat_number]`, seat.seat_number);
                formData.append(`products[${index}][${key}][${seatKey}][section_name]`, seat.section_name);
              });
            }
          }
        });
        formData.append("event_id", this.eventId);
        formData.append('event_seatmap_id',this.eventSeatMapId);
        formData.append('date',this.date);
        if (this.bookingForm.sales_team_id) {
          formData.append("sales_team_id", this.bookingForm.sales_team_id);
        }
        this.$http
            .post(`venues/events/booking`, formData, {headers: {"Content-Type": "multipart/form-data; boundary=${form._boundary}",},})
            .then((response) => {
              this.hideLoader();
              if (response.status === 200 && response.data.status === true) {
                this.showSuccess("Booking successfully added");
                let data = response.data.data;
                this.event_customers = [];
                this.displayedParticipant = 1;
                this.bookingForm = null;
                this.bookingForm = { price: 0, opt_marketing: false };
               this.$emit("booked", data.id);
              }else{
                this.showError("Something went wrong");
              }
            })
            .catch((error) => {
              this.hideLoader();
              this.errorChecker(error);
            });
      },
      timeFormat(time) {
        return moment(time, "HH:mm:ss").format("hh:mm a");
      },
      searchMember(mobile, id, first_name, last_name, index = 0,card_number=null) {
        if(index !== 0){
          return;
        }
        let query = "";
        if(card_number){
          query = `field=display_number&search=${card_number}`;
        }else if (typeof id != "undefined" && id != null) {
          query = `field=id&search=${id}`;
        }else {
          if (typeof mobile != "undefined") {
            query = `field=mobile_number&search=${mobile}`;
          }
        }
        console.log(id, first_name, last_name, mobile);
        this.isSearchLoading = true;
        if (query != "") {
          this.$http
              .get(`venues/memberships/members/filters?${query}`)
              .then((response) => {
                if (response.status == 200) {
                  let data = response.data.data;
                  if (data.length > 0) {
                    this.bookingForm.promotion_code = null;
                    if (data[0].card_number) {
                      this.bookingForm.card_number = data[0].card_number;
                      this.event_customers[index].customer_type = "member";
                      this.$set(this.event_customers[index], "card_number", data[0].card_number);
                      this.$forceUpdate();
                      if (this.bookingForm && this.bookingForm.products && this.bookingForm.products.length > 0) {
                        this.verifyBenefit("membership");
                      }
                    }else {
                      if (index === 0) {
                        if (this.event_customers[0].customer_type == "member") {
                          this.event_customers[0].customer_type = "normal";
                          this.$set(this.event_customers[0], "customer_type", "normal");
                          this.$set(this.event_customers[0], "membership_id", null);
                        }
                        this.event_customers[0].member = null;
                        this.event_customers[0].customer_type = 'normal';
                        this.$set(this.event_customers[0], "card_number", null);
                        if (this.bookingForm && this.bookingForm.promotion_code == null) {
                          this.clearBenefit();
                        }
                      }
                    }
                  } else {
                    this.bookingForm.member = null;
                    this.event_customers[index].customer_type = 'normal';
                    this.$set(this.bookingForm, "card_number", null);
                    if (this.bookingForm.promotion_code == null) {
                      this.clearBenefit();
                    }
                  }
                  this.$forceUpdate();
                }
              })
              .catch((error) => {
                this.errorChecker(error);
              });
        }
      },
      changeIdProofTypeId(id_proof_type_id, index) {
        if (index !== null) {
          if (this.event_customers && this.event_customers.length) {
            let objType = this.event_customers[index].customer_documents.find((x) => (x.id_proof_type_id === id_proof_type_id));
            if (typeof objType !== "undefined" && objType.id_proof_type_id) {
              this.event_customers[index].id_proof_number = objType.id_proof_number;
              this.event_customers[index].id_proof_path = objType.id_proof_path;
            } else {
              this.event_customers[index].id_proof_number = null;
              this.event_customers[index].id_proof_path = null;
            }
          } else {
            console.log("document length 0");
          }
        }
      },
    },
  };
  </script>
  <style lang="scss" scoped>
  $card-outlined-border-width: 3px;



  .top-member-radio-div {
    display: flex;
    flex-wrap: nowrap;
    flex-direction: row;
    justify-content: flex-start;
    gap: 10px;
    /* flex-flow: row nowrap; */
  }
  .top-member-radio-div .member-radio {
    margin-top: 3px;
  }

  .top-member-radio-div .customer-list {
    width: 30%;
  }

  .top-member-radio-div .member-card-div {
    width: 32%;
  }
  ::v-deep {
    .gender-radio {
      padding-top: 0 !important;
      .v-radio {
        display: flex;
        justify-content: center;
        border: 1px solid #A6A6A6;
        border-radius: 0.25rem;
        flex-grow: 1;
        label {
          justify-content: center;
        }
        &.v-item--active {
          border: 1px solid #112A46;
          background-color: rgba(17, 42, 70, 0.1);
          color: #112A46;
          label {
            font-weight: 600 !important;
          }
        }
        &:last-child{
          margin-right: 0 !important;
        }
      }
      .v-label {
        min-width: 7.8rem;
        padding: 0.65rem;
      }
      .v-input--selection-controls__input {
        display: none !important;
      }
    }
    .reader-button{
      width: 36px;
      min-width: 36px !important;
    }
    .member-radio .v-input{
      margin-top: 0px;
    }
  }
  </style>
