<template>
  <v-container fluid>
    <!-- Navigation -->
    <v-row>
      <v-col cols="12">
        <v-btn icon @click="goBack">
          <v-icon>mdi-arrow-left</v-icon> Back
        </v-btn>
      </v-col>
    </v-row>

    <!-- Main Layout -->
    <v-row>
      <!-- Map Sections Sidebar -->
      <v-col cols="12" md="3">
        <v-card class="custom-card-map-section">
          <v-card-title class="d-flex justify-space-between pb-1 pt-1 card-title-heading">
            <span class="font-16">Map Sections</span>
            <span class="font-12 text-underline text-blue pointer">View Map</span>
          </v-card-title>
          <v-list>
            <v-list-item
                v-for="section in sections"
                :key="section.id"
                :class="{ 'active-section': selectedSectionId === section.section_id }"
                @click="selectSection(section.section_id)"
                class="border-bottom"
            >
              <v-avatar small :color="section.color" size="12" class="mr-2"/>
              <v-list-item-content>
                <v-list-item-title class="font-14 font-semibold">{{ section.section_name }}</v-list-item-title>
                <v-list-item-subtitle class="font-12">
                  <span class="meta-desc">Total Seats: {{ getTotalSeats(section) }}, Total Assigned: {{ getAssignedCount(section) }}</span>
                  <span class="meta-desc">Ticket: {{ getAssignedTickets(section).join(', ') }}</span>
                </v-list-item-subtitle>
              </v-list-item-content>

            </v-list-item>
          </v-list>
        </v-card>
      </v-col>
      <!-- Configuration Panel -->
      <v-col cols="12" md="9">
        <v-card class="custom-card-seat-config">
          <v-card-title class="d-flex justify-space-between pb-1 pt-1 card-title-heading ticket-card">
            <span class="font-16">Assign Tickets</span>
            <span class="font-16">{{ selectedSection?selectedSection.section_name: ""}}</span>
            <span class="font-16">Total Assigned Tickets: {{ assignedSeatCount }}</span>
          </v-card-title>
          <v-card-text>
            <v-card class="inner-custom-card-seat-config inner-card mt-3">
              <v-card-text>
                <div class="d-flex flex-wrap align-start justify-space-between w-100">
                  <!-- Left Column (Ticket Type + Assign Ticket) -->
                  <div class="d-flex flex-wrap">
                    <!-- Ticket Type Column -->
                    <div class="d-flex flex-column mr-6">
                      <label class="">Ticket Type</label>
                      <v-radio-group
                          v-model="seatSelectionType"
                          class="d-flex mt-0"
                          mandatory
                          row
                          @change="changeSeatSelectionType"
                      >
                        <v-radio value="general" class="custom-radio mr-2" color="red" label="General" />
                        <v-radio value="specific" class="custom-radio" color="red" label="Specific" />
                      </v-radio-group>
                    </div>

                    <!-- Assign Ticket Column -->
                    <div class="d-flex flex-column mr-6" v-if="(seatSelectionType === 'general' || (seatSelectionType === 'specific' && selectedSeats.length))">
                      <label class="">Assign Ticket</label>
                      <v-select
                          v-model="selectedTicketId"
                          :items="eventTickets"
                          label=""
                          outlined
                          dense
                          style="max-width: 200px;"
                          item-text="product_name"
                          item-value="id"
                          class="q-autocomplete shadow-0"
                          required
                          hide-details="auto"
                          clearable
                          @change="assignTicketToSelectedSeats"
                      />
                    </div>
                  </div>
                  <!-- Right-Aligned Deactivate Seats Column -->
                  <div class="d-flex flex-row action-text mt-3 " v-if="seatSelectionType === 'specific' && selectedSeats.length">
                    <label class="text-red pl-2 border-red cursor-pointer" @click="blockUnBlockSeats('block')">
                      <LockIcon class="w-4 h-4" />
                      <span>Block</span>
                    </label>
                    <label class="pl-2" @click="blockUnBlockSeats('unblock')"><UnLockIcon /> Unblock</label>
                    <label class="pl-2" @click="blockUnBlockSeats('reset')"><ResetIcon/> Reset</label>
                    <!-- Your deactivate control here (e.g., switch, checkbox, etc.) -->
                  </div>
                </div>

                <!-- No v-row here to avoid unwanted gutters; use flex only -->
                <div v-if="selectedSection && selectedSection.rows && seatSelectionType === 'specific'">
                  <v-checkbox
                      v-model="isSelectAllSeats"
                      label="Select All"
                      hide-details
                      dense
                      class="mr-4 mb-2"
                      @change="toggleSelectAllSeats"
                  />
                  <div
                      v-for="(row, rowIndex) in selectedSection.rows"
                      :key="rowIndex"
                      class="d-flex align-center mb-2"
                  >
                    <!-- Row-level checkbox -->
                    <v-checkbox
                        v-model="row.selected"
                        hide-details
                        dense
                        class="mr-4"
                        @change="toggleRowSelection(row)"
                    />

                    <!-- Seat spans -->
                    <div class="d-flex flex-wrap">
                      <span v-for="seat in row.seats" :key="seat.id"  :class="['seat-box mr-1', getSeatClass(seat)]" @click="selectSeat(seat.id)">{{ seat.seat_number }}</span>
                    </div>
                  </div>
                </div>
              </v-card-text>
            </v-card>
            <!-- Legend -->
            <div class="d-flex flex-wrap align-center mt-4">
              <div
                  v-for="legend in legends"
                  :key="legend.label"
                  class="d-flex align-center mr-4 mb-2"
              >
                <v-avatar size="12" :color="legend.color" class="mr-2" />
                <span>{{ legend.label }}</span>
              </div>
            </div>
          </v-card-text>
        </v-card>
        <div class="d-flex justify-end mt-2">
          <v-spacer />
          <v-btn color="primary" @click="saveSeatmapConfiguration">Save</v-btn>
        </div>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import LockIcon from '@/assets/images/seatmap/red-lock.svg';
import UnLockIcon from '@/assets/images/seatmap/unlock.svg';
import ResetIcon from '@/assets/images/seatmap/reset.svg';

export default {
  props: {
    event: { type: Object, default: null},
  },
  components: {
    LockIcon,UnLockIcon,ResetIcon
  },
  data() {
    return {
      eventId: null,
      valid: true,
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      seatSelectionType: 'general',
      sections: [],
      originalSeatsData: [],
      eventTickets: [],
      renderSection: false,
      selectedSectionId: null,
      selectedSection: null,
      isSelectAllSeats:null,
      legends: [
        { label: 'Assigned', color: '#4CAF50' },
        { label: 'Not Assigned', color: '#708090' },
        { label: 'Selected', color: '#5CC5D4' },
        { label: 'Blocked', color: '#B0B0B0' },
      ],
      selectedFilter: 'All',
      seatRows: [],
      selectedTicketId: null,
      selectedSeats: [],
      ticketStatus: {
         'ASSIGNED': 'assigned',
         'NOT_ASSIGNED': 'unassigned',
         'BLOCKED': 'blocked',
      },
    };
  },
  mounted(){
    if (typeof this.$route.params.data != "undefined") {
      this.eventId = parseInt(atob(this.$route.params.data));
      this.getEventSeatmap(this.eventId);
    }
  },
  computed: {
    assignedSeatCount() {
      if (!this.selectedSection) return 0;
      return this.selectedSection.rows.reduce((total, row) => {
        return total + row.seats.filter(seat => seat.event_ticket_id).length;
      }, 0);
    },
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    getAssignedTickets(section) {
      const ticketIds = new Set();
      section.rows.forEach(row => {
        row.seats.forEach(seat => {
          if (seat.event_ticket_id) {
            ticketIds.add(seat.event_ticket_id);
          }
        });
      });
      // Map ticket IDs to product names
      const ticketNames = Array.from(ticketIds).map(id => {
        const ticket = this.eventTickets.find(t => t.id === id);
        return ticket ? ticket.product_name : 'Unknown Ticket';
      });
      return ticketNames;
    },
    getAssignedCount(section) {
      return section.rows.reduce((count, row) => {
        return count + row.seats.filter(seat => seat.event_ticket_id).length;
      }, 0);
    },
    getTotalSeats(section) {
      return section.rows.reduce((count, row) => count + row.seats.length, 0);
    },
    getSeatClass(seat) {
      if (this.isSeatSelected(seat.id)) return 'selected';
      return seat.status;
      // if (seat.status) return 'assigned';
      // return 'unassigned';
    },
    selectSection(section_id) {
      this.selectedSectionId = section_id;
      this.isSelectAllSeats = null;
      const s = this.sections.filter(section => section.section_id === section_id);
      if(s && s[0]){
        this.selectedSection = s[0];
        const ticketNamesArray= this.getAssignedTickets(s[0]);
        console.log(ticketNamesArray);
        if(ticketNamesArray.length > 1){
          this.seatSelectionType = "specific";
        }else if(ticketNamesArray.length === 1){
          this.seatSelectionType = "general";
          console.log("ticketNamesArray[0]",ticketNamesArray[0]);
          const ticket = this.eventTickets.find(t => t.product_name === ticketNamesArray[0]);
          console.log("ticket",ticket);
          this.selectedTicketId = ticket.id;
        }else{
          this.selectedTicketId = null;
        }
      }
    },
    confirmActions(data) {
      if (data.type == "delete_section") {
        this.deleteSection(data.id);
      }
      this.confirmModel.id = null;
    },
    getEventSeatmap(eventId) {
        this.showLoader("Loading");
        this.$http.get(`venues/events/event-seatmap/configuration?event_id=${eventId}`)
            .then((response) => {
              if (response.status == 200) {
                if (response.data && response.data.data) {
                  this.sections = response.data.data.sections;
                  this.eventTickets = response.data.data.event_tickets;
                  // Flatten all original seats into a Map for fast comparison
                  this.originalSeatsData = new Map();
                  response.data.data.sections.forEach(section => {
                    section.rows.forEach(row => {
                      row.seats.forEach(seat => {
                        this.originalSeatsData.set(seat.id, {
                          event_ticket_id: seat.event_ticket_id,
                          status: seat.status
                        });
                      });
                    });
                  });
                }else{
                  this.sections = [];
                  this.eventTickets = [];
                  this.originalSeatsData = [];
                  this.showError('Data not found');
                }
                this.hideLoader();
              }
            })
            .catch((error) => {
              console.log(error);
              this.hideLoader();
            });
    },
    filterBookedSeats(mapSection) {
      if (mapSection.booked_seats && mapSection.booked_seats.length) {
        const bookedSeats = mapSection.booked_seats;
        mapSection.seats.forEach((seat) => {
          let seatFind = bookedSeats.find((bs) => bs.seat_name === seat.seat_name && bs.seat_map_section_id === seat.seat_map_section_id);
          if (seatFind !== undefined) {
            seat.is_booked = true;
            seat.status = seatFind.order_status_id == 4?'paid':'unpaid';
          }
        })
      }
      this.section = Object.assign({}, mapSection);
    },
    changeSeatSelectionType(){
      console.log(this.seatSelectionType)
    },
    saveSeatmapConfiguration() {
      this.section = null;
      this.setSeatmapTicketData();
    },
    selectSeat(seatId) {
      const index = this.selectedSeats.indexOf(seatId);
      if (index === -1) {
        this.selectedSeats.push(seatId); // select
      } else {
        this.selectedSeats.splice(index, 1); // deselect
      }
    },
    isSeatSelected(seatId) {
      return this.selectedSeats.includes(seatId);
    },
    blockUnBlockSeats(action = "block") {
      if (!this.selectedSection) return;
      if(action === "block") {
        this.selectedSection.rows.forEach(row => {
          row.seats.forEach(seat => {
            if (this.selectedSeats.includes(seat.id)) {
              seat.status = this.ticketStatus.BLOCKED;
            }
          });
        });
        console.log("blocked");
        console.log(this.selectedSection.rows);
      }else if(action === "unblock") {
        this.selectedSection.rows.forEach(row => {
          row.seats.forEach(seat => {
            if (this.selectedSeats.includes(seat.id)) {
              console.log("seat",seat);
              if(seat.event_ticket_id){
                seat.status = this.ticketStatus.ASSIGNED;
              }else{
                seat.status = this.ticketStatus.NOT_ASSIGNED;
              }
            }
          });
        });
      }else if(action === "reset") {
        this.selectedSection.rows.forEach(row => {
          row.seats.forEach(seat => {
            if (this.selectedSeats.includes(seat.id)) {
              if(seat.event_ticket_id){
                seat.event_ticket_id = null;
              }
              seat.status = this.ticketStatus.NOT_ASSIGNED;
            }
          });
        });
        this.isSelectAllSeats = null;
      }
      // Optional: clear selection after assigning
      this.selectedSeats = [];
      this.selectedSection.rows.forEach(row => (row.selected = false));
      this.selectedTicketId = null;
    },
    // Handles row-level checkbox selection
    toggleRowSelection(row) {
      const seatIds = row.seats.map(seat => seat.id);
      if (row.selected) {
        // Add all seat IDs in this row
        seatIds.forEach(id => {
          if (!this.selectedSeats.includes(id)) {
            this.selectedSeats.push(id);
          }
        });
      } else {
        // Remove all seat IDs in this row
        this.selectedSeats = this.selectedSeats.filter(id => !seatIds.includes(id));
      }
    },
    toggleSelectAllSeats(){
      if (!this.selectedSection) return;
      this.selectedSeats = [];
      if(this.isSelectAllSeats){
        this.selectedSection.rows.forEach(row => {
          let seatIds = row.seats.map(seat => seat.id);
          row.selected = true;
          seatIds.forEach(id => {
            this.selectedSeats.push(id);
          });
        });
      }else{
        this.selectedSeats = [];
        this.selectedSection.rows.forEach(row => (row.selected = false));
      }
    },
    assignTicketToSelectedSeats() {
      console.log("yes its called: ",this.selectedTicketId);
      if (!this.selectedTicketId ||  !this.selectedSection) return;
      this.selectedSection.rows.forEach(row => {
        row.seats.forEach(seat => {
          if (this.seatSelectionType === 'general') {
            seat.event_ticket_id = this.selectedTicketId;
            seat.status = this.ticketStatus.ASSIGNED;
          }else if (this.selectedSeats.includes(seat.id)) {
            seat.event_ticket_id = this.selectedTicketId;
            seat.status = this.ticketStatus.ASSIGNED;
          }
        });
      });

      // Optional: clear selection after assigning
      this.selectedSeats = [];
      this.selectedSection.rows.forEach(row => (row.selected = false));
      this.selectedTicketId = null;
      if(this.isSelectAllSeats){
        this.isSelectAllSeats = null;
      }
    },
    getModifiedSeatData() {
      const modified = [];
      this.sections.forEach(section => {
        section.rows.forEach(row => {
          row.seats.forEach(seat => {
            const original = this.originalSeatsData.get(seat.id);
            const currentTicketId = seat.event_ticket_id ?? null;
            const currentStatus = seat.status ?? null;
            const originalTicketId = original?.event_ticket_id ?? null;
            const originalStatus = original?.status ?? null;
            // Compare current values with original values
            const isChanged = currentTicketId !== originalTicketId || currentStatus !== originalStatus;
            if (isChanged) {
              modified.push({
                id: seat.id,
                event_ticket_id: currentTicketId,
                status: currentStatus
              });
            }
          });
        });
      });
      return modified;
    },
    setSeatmapTicketData() {
      if (this.eventId && this.sections) {
        const jsonSeatData = this.getModifiedSeatData();
        if (jsonSeatData.length === 0) {
          this.showError("No seat changes to save");
          return;
        }
        const payload = {
          event_id: this.eventId,
          seat_data: jsonSeatData
        };
        this.showLoader("Processing");
        this.$http.post(`venues/events/event-seatmap/update-seats`, payload)
            .then((response) => {
              if (response.status == 200) {
                if (response.data && response.data) {
                    this.$router.push({
                      name: "ViewEvent",
                      params: {data: btoa(this.eventId)},
                    });
                    this.showSuccess('Data saved successfully.');
                }else{
                  this.showError('Data not found');
                }
                this.hideLoader();
              }
            })
            .catch((error) => {
              console.log(error);
              this.hideLoader();
            });
      }else{
        this.showError("No seat changes to save");
        return;
      }
    },
  }
};
</script>
<style>
.seat-map-editor {
  position: relative;
}
.parent-map-canvas{
  width: 100%;
  overflow: auto;
  margin-top:5px;
  min-height: 480px;
}
.active-section {
  background-color: #EEF7F7;
}
.v-card.custom-card-seat-config,.v-card.custom-card-map-section{
  border: 1px solid #e2e2e2;
  border-radius: 10px;
  box-shadow: none !important;
}
.inner-custom-card-seat-config{
  border: 1px solid #e2e2e2;
  border-radius: 10px;
}
.card-title-heading{
  border-bottom: 1px solid #e2e2e2;
}
.card-title-heading.ticket-card{
  background-color: #112A46;
  color: #fff;
}
.seat-box {
  width: 24px;
  height: 24px;
  flex-shrink: 0;

  display: flex;
  align-items: center;
  justify-content: center;

  background-color: #708090;      /* Use -color for clarity */
  border: 1px solid #ccc;
  border-radius: 50%;

  color: #fff;
  cursor: pointer;

  font-family: 'Inter', sans-serif;
  font-size: 8px;
  font-weight: 500;
  line-height: 1;
  font-style: normal;

  user-select: none;              /* Optional: prevent text highlight */
  transition: background-color 0.2s ease; /* Optional: smoother interaction */
}
.seat-box.selected {
  background-color: #5CC5D4;
  border-color: #0097a7;
}
.seat-box.assigned {
  background-color: #4CAF50;
  border-color: #29872c;
}

.seat-box.unassigned {
  background-color: #708090;
  border-color: #617487;
}
.seat-box.blocked {
  background-color: #B0B0B0;
  border-color: #8c8c8c;
}
span.meta-desc {
  display: block;
  line-height: 1.4;
  font-size: 12px;
}
.action-text label {
  border: 1px solid #dcdcdc;
  padding: 8px 8px;
  margin: 2px;
  text-decoration: none;
  border-radius: 4px;
  min-width: 80px;
  text-align: center;
  font-weight: 500;

  display: inline-flex;
  align-items: center;
  gap: 3px;
  font-size: 12px;
}
.action-text label.text-red{
  color:#E50000;
  border-color: #E50000;
}
.action-text label:hover{
  cursor: pointer;
  opacity: 0.8;
}

</style>