<template>
  <v-container fluid class="pt-0">
    <!-- Main Layout -->
    <v-row>
      <!-- Map Sections Sidebar -->
      <v-col cols="12" md="3">
        <v-card class="custom-card-map-section">
          <v-card-title class="d-flex justify-space-between pb-1 pt-1 card-title-heading">
            <span class="font-16">Map Sections</span>
            <span class="font-12 text-underline text-blue pointer" @click="showEventSeatMapDialog = true">View Map</span>
          </v-card-title>
          <v-list class="section-list">
            <v-list-item
                v-for="section in sections"
                :key="section.id"
                :class="{ 'active-section': selectedSectionId === section.section_id }"
                @click="selectSection(section.section_id)"
                class="border-bottom"
            >
              <v-list-item-content>
                <v-list-item-title class="font-14 font-semibold">{{ section.section_name }}</v-list-item-title>
                <v-list-item-subtitle class="font-12">
                  <span class="meta-desc">Total Seats: {{ getTotalSeats(section) }}, Total Assigned: {{ getAssignedCount(section) }}</span>
                  <span class="meta-desc">Ticket: {{ getAssignedTickets(section).join(', ') }}</span>
                </v-list-item-subtitle>
              </v-list-item-content>

            </v-list-item>
          </v-list>
        </v-card>
      </v-col>
      <!-- Configuration Panel -->
      <v-col cols="12" md="9">
        <v-card class="custom-card-seat-config">
          <v-card-title class="d-flex justify-space-between pb-1 pt-1 card-title-heading ticket-card">
            <span class="font-16">Book Tickets</span>
            <span class="font-16">{{ selectedSection?selectedSection.section_name: ""}}</span>
          </v-card-title>
          <v-card-text>
            <v-card class="inner-custom-card-seat-config inner-card mt-3">
              <v-card-text>
                <!-- No v-row here to avoid unwanted gutters; use flex only -->
                <div v-if="selectedSection && selectedSection.rows && eventTickets && eventTickets.length > 0">
                  <div v-for="(row, rowIndex) in selectedSection.rows" :key="rowIndex" class="d-flex align-center mb-2">
                    <!-- Seat spans -->
                    <div class="d-flex flex-wrap">
                      <span v-for="seat in row.seats"
                            :key="seat.id"
                            :class="['seat-box mr-1', getSeatClass(seat)]"
                            @click="selectSeat(seat)"
                            @mouseenter="onSeatHover($event, seat)"
                            @mouseleave="hideTooltip"
                      >
                        {{ seat.seat_number }}
                      </span>
                    </div>
                  </div>
                </div>
                <div v-else>
                  <div class="d-flex flex-wrap">Please select section</div>
                </div>
              </v-card-text>
            </v-card>
            <!-- Legend -->
            <div class="d-flex flex-wrap align-center mt-4 seat-legends">
              <div
                  v-for="legend in legends"
                  :key="legend.label"
                  class="d-flex align-center mr-4 mb-2"
              >
                <v-avatar size="12" :color="legend.color" class="mr-2" />
                <span>{{ legend.label }}</span>
              </div>
            </div>
          </v-card-text>
        </v-card>
        <div class="align-center mt-4">
          <div v-for="ticketGroup in selectedProducts" :key="ticketGroup.ticket_id" class="selected-ticket-group mb-2">
            <strong>{{ ticketGroup.name }}</strong>:
            <span v-for="seat in ticketGroup.seats" :key="seat.label" class="seat">{{ seat.seat_number }} <button @click="removeSeat(seat)" class="remove-seat">x</button></span> — AED {{ ticketGroup.total_price }}
          </div>
        </div>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="12" md="12">
        <v-divider class="mt-2"/>
        <div class="d-flex justify-end mt-2">
          <v-spacer />
          <span class="mr-2 total-span">Total (inc.Tax): AED {{getTotalPrice}}</span>
          <v-btn color="primary" @click="bookingContinue" style="text-transform: capitalize">Continue</v-btn>
        </div>
      </v-col>
    </v-row>
    <v-tooltip
        v-if="hoveredSeat"
        v-model="showTooltip"
        :top="false"
        :left="false"
        :bottom="true"
        :right="false"
        :position-x="tooltipX"
        :position-y="tooltipY"
        open-on-hover
        transition="fade-transition"
    >
      <div class="text-left text-body-2 pa-2">
        <strong>Ticket:</strong> {{ hoveredSeat.ticket_name || 'N/A' }}<br>
        <strong>Price:</strong> {{ hoveredSeat.ticket_price ? 'AED ' + hoveredSeat.ticket_price : 'N/A' }}<br>
      </div>
    </v-tooltip>

    <EventBookingSeatMapForm
        :showEventBookingSeatMapForm="showEventBookingSeatMapForm"
        :selectedProducts="selectedProducts"
        :maxParticipants="allowed_max_participants"
        :eventSeatMapId="eventSeatMapId"
        :eventId="eventId"
        :date="date"
        @booked="openOrderCloseBooking"
        @close="closeEventBookingSeatMapForm"
    />
    <order-details
        :id="orderId"
        @close="paymentDone"
        @paymentDone="paymentDone"
    ></order-details>
    <EventSeatMapImageDialog :showEventSeatMapDialog="showEventSeatMapDialog" :seatMapImage="eventSeatMapImage" @close="showEventSeatMapDialog = false"/>
  </v-container>
</template>

<script>
import EventBookingSeatMapForm from "./EventBookingSeatMapForm.vue";
import moment from "moment/moment";
import OrderDetails from "@/components/Order/OrderDetails.vue";
import EventSeatMapImageDialog from "@/components/Event/EventSeatMapImageDialog.vue";
export default {
  props: {
    event: { type: Object, default: null},
  },
  components:{
    EventSeatMapImageDialog,
    OrderDetails,
    EventBookingSeatMapForm,
  },
  data() {
    return {
      orderId: null,
      eventId: null,
      valid: true,
      confirmModel: {
        id: null,
        title: null,
        description: null,
      },
      seatSelectionType: 'general',
      sections: [],
      originalSeatsData: [],
      eventTickets: [],
      renderSection: false,
      selectedSectionId: null,
      selectedSection: null,
      isSelectAllSeats:null,
      legends: [
        { label: 'Assigned', color: '#4CAF50' },
        { label: 'Not Assigned', color: '#708090' },
        { label: 'Selected', color: '#5CC5D4' },
        { label: 'Blocked', color: '#B0B0B0' },
        { label: 'Booked', color: '#E50000' },
      ],
      selectedFilter: 'All',
      seatRows: [],
      selectedTicketId: null,
      selectedSeats: [],
      ticketStatus: {
        'ASSIGNED': 'assigned',
        'NOT_ASSIGNED': 'unassigned',
        'BLOCKED': 'blocked',
      },
      hoveredSeat: null,
      tooltipX: 0,
      tooltipY: 0,
      showTooltip: false,
      showEventBookingSeatMapForm: false,
      selectedProducts:[],
      allowed_max_participants: 0,
      eventSeatMapId:0,
      date: moment().format("YYYY-MM-DD"),
      eventSeatMapImage: null,
      showEventSeatMapDialog: false,
    };
  },
  mounted(){
    if (typeof this.$route.params.data != "undefined") {
      this.eventId = parseInt(atob(this.$route.params.data));
      this.getEventSeatmap(this.eventId);
      console.log("event",this.event);
      this.date = moment(this.event.start_date, "YYYY-MM-DD").isAfter(moment())
          ? moment(this.event.start_date, "YYYY-MM-DD").format("YYYY-MM-DD")
          : moment().format("YYYY-MM-DD");
    }
  },
  computed: {
    getTotalPrice(){
      let total = 0;
      if(this.selectedSeats.length){
        this.selectedSeats.forEach(seat => {
          let et = this.eventTickets.find( (et) => et.id === seat.event_ticket_id);
          if(et){
            total += Number(et.total_price);
          }
        });
      }
      return total;
    },

  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    getSelectedTicketsGrouped() {
      const grouped = {};
      let participantCount = 0;
      if (this.selectedSeats.length) {
        this.selectedSeats.forEach(seat => {
          const et = this.eventTickets.find(et => et.id === seat.event_ticket_id);
          if (!et) return;
          if (!grouped[et.id]) {
            grouped[et.id] = {
              event_id: et.event_id,
              event_ticket_id: et.id,
              name: et.product_name,
              participant_count: et.participant_count,
              price: et.price,
              quantity: 0,
              tax_amount: et.tax_amount,
              total_price: et.total_price,
              product_id: et.product_id,
              product_type_id: et.product_type_id,
              seats: []
            };
          }
          grouped[et.id].seats.push({
            ...seat, // keep full seat info so you can identify and remove
            product_id: et.product_id
          });
          grouped[et.id].quantity = grouped[et.id].seats.length;
          grouped[et.id].total_price = et.total_price * grouped[et.id].quantity ;
          participantCount += et.participant_count;
        });
      }
      const res = Object.values(grouped);
      console.log("Res",res);
      this.selectedProducts = [...res];
      this.allowed_max_participants = participantCount;
    },
    removeSeat(seat){
      const index = this.selectedSeats.findIndex((s) => s.id === seat.id);
      if (index !== -1) {
        this.selectedSeats.splice(index, 1);
        this.getSelectedTicketsGrouped();
      }
    },
    closeEventBookingSeatMapForm(){
      this.showEventBookingSeatMapForm = false;
    },
    getAssignedTickets(section) {
      const ticketIds = new Set();
      section.rows.forEach(row => {
        row.seats.forEach(seat => {
          if (seat.event_ticket_id) {
            ticketIds.add(seat.event_ticket_id);
          }
        });
      });
      // Map ticket IDs to product names
      const ticketNames = Array.from(ticketIds).map(id => {
        const ticket = this.eventTickets.find(t => t.id === id);
        return ticket ? ticket.product_name : 'Unknown Ticket';
      });
      return ticketNames;
    },
    getAssignedCount(section) {
      return section.rows.reduce((count, row) => {
        return count + row.seats.filter(seat => seat.event_ticket_id).length;
      }, 0);
    },
    getTotalSeats(section) {
      return section.rows.reduce((count, row) => count + row.seats.length, 0);
    },
    getSeatClass(seat) {
      if (this.isSeatSelected(seat.id) >= 0) return 'selected';
      return seat.status;
    },
    getTicketName(ticketId) {
      console.log("ticketID",ticketId);
      if (!ticketId) return 'No ticket assigned';

      const ticket = this.eventTickets.find(t => t.id === ticketId);
      if (!ticket) {
        console.warn('Ticket not found for ID:', ticketId);
        return 'No ticket assigned';
      }
      return ticket.name;
    },
    selectSection(section_id) {
      this.selectedSectionId = section_id;
      this.isSelectAllSeats = null;
      const s = this.sections.filter(section => section.section_id === section_id);
      if(s && s[0]){
        this.selectedSection = s[0];
        const ticketNamesArray= this.getAssignedTickets(s[0]);
        console.log(ticketNamesArray);
        if(ticketNamesArray.length > 1){
          this.seatSelectionType = "specific";
        }else if(ticketNamesArray.length === 1){
          this.seatSelectionType = "general";
          console.log("ticketNamesArray[0]",ticketNamesArray[0]);
          const ticket = this.eventTickets.find(t => t.product_name === ticketNamesArray[0]);
          console.log("ticket",ticket);
          this.selectedTicketId = ticket.id;
        }else{
          this.selectedTicketId = null;
        }
      }
    },
    confirmActions(data) {
      if (data.type == "delete_section") {
        this.deleteSection(data.id);
      }
      this.confirmModel.id = null;
    },
    getEventSeatmap(eventId) {
      this.showLoader("Loading");
      this.$http.get(`venues/events/event-seatmap/configuration?event_id=${eventId}`)
          .then((response) => {
            if (response.status == 200) {
              if (response.data && response.data.data) {
                this.sections = response.data.data.sections;
                this.eventSeatMapImage = response.data.data.event_seatmap_image;
                this.eventTickets = response.data.data.event_tickets;
                // Flatten all original seats into a Map for fast comparison
                this.originalSeatsData = new Map();
                response.data.data.sections.forEach(section => {
                  section.rows.forEach(row => {
                    row.seats.forEach(seat => {
                      this.originalSeatsData.set(seat.id, {
                        event_ticket_id: seat.event_ticket_id,
                        status: seat.status
                      });
                    });
                  });
                });
                // Reselect the same section after refresh
                if (this.selectedSectionId) {
                  const s = this.sections.find(section => section.section_id === this.selectedSectionId);
                  if (s) {
                    this.selectedSection = s;
                  }
                }
                this.eventSeatMapId = this.sections[0]?.event_seatmap_id ?? null;
                console.log("this.eventSeatMapId", this.eventSeatMapId);
              }else{
                this.sections = [];
                this.eventTickets = [];
                this.originalSeatsData = [];
                this.eventSeatMapId = null;
                this.selectedSection = null;
                this.eventSeatMapId = null;
                this.eventSeatMapImage = null;
                this.showError('Data not found');
              }
              this.hideLoader();
            }
          })
          .catch((error) => {
            console.log(error);
            this.hideLoader();
          });
    },
    bookingContinue() {
      this.section = null;
      this.showEventBookingSeatMapForm=true;
    },
    selectSeat(seat) {
      if(seat.status === 'booked'){
        this.showError("Seat already booked");
      }else if(seat.status === 'blocked' || seat.status === 'unassigned' || seat.event_ticket_id === null || seat.event_ticket_id === "null"){
        this.showError('Seat not available');
      }else{
        const index = this.selectedSeats.findIndex((s) => s.id === seat.id);
        if (index === -1) {
          const et = this.eventTickets.find((s) => s.id === seat.event_ticket_id);
          if(et && et.ticket_type !== "I"){
            this.showError("Only individual ticket supported");
            return false;
          }
          this.selectedSeats.push(seat); // select
          this.getSelectedTicketsGrouped();
        } else {
          this.selectedSeats.splice(index, 1); // deselect
          // remove seats
          this.getSelectedTicketsGrouped();
        }
      }
    },
    isSeatSelected(seatId) {
      let index =  this.selectedSeats.findIndex((s) => s.id === seatId);
      return index;
    },
    getModifiedSeatData() {
      const modified = [];
      this.sections.forEach(section => {
        section.rows.forEach(row => {
          row.seats.forEach(seat => {
            const original = this.originalSeatsData.get(seat.id);
            const currentTicketId = seat.event_ticket_id ?? null;
            const currentStatus = seat.status ?? null;
            const originalTicketId = original?.event_ticket_id ?? null;
            const originalStatus = original?.status ?? null;
            // Compare current values with original values
            const isChanged = currentTicketId !== originalTicketId || currentStatus !== originalStatus;
            if (isChanged) {
              modified.push({
                id: seat.id,
                event_ticket_id: currentTicketId,
                status: currentStatus
              });
            }
          });
        });
      });
      return modified;
    },
    onSeatHover(event, seat) {
      if(seat.event_ticket_id){
        let et = this.eventTickets.find(et => et.id === seat.event_ticket_id);
        this.hoveredSeat = {
          ticket_name: et.product_name,
          ticket_price: Number(et.total_price || 0).toFixed(2)
        };
        this.tooltipX = event.clientX;
        this.tooltipY = event.clientY;
        this.showTooltip = true;
      }
    },
    hideTooltip() {
      this.showTooltip = false;
    },
    openOrderCloseBooking(orderId) {
      this.showEventBookingSeatMapForm = false;
      this.allowed_max_participants = 1;
      this.section = null;
      this.orderId = orderId;
      //this.sections = [];
      //this.selectedSection = null;
      //this.selectedSeats = []; // Clear old selections
      //this.selectedProducts = []; // Optional: clear grouped tickets
      //this.getEventSeatmap(this.eventId);
    },
    paymentDone(){
      this.orderId = null;
      this.sections = [];
      this.selectedSection = null;
      this.selectedSeats = []; // Clear old selections
      this.selectedProducts = []; // Optional: clear grouped tickets
      this.getEventSeatmap(this.eventId);
    },
  }
};
</script>
<style>
.seat-map-editor {
  position: relative;
}
.parent-map-canvas{
  width: 100%;
  overflow: auto;
  margin-top:5px;
  min-height: 480px;
}
.active-section {
  background-color: #EEF7F7;
}
.v-card.custom-card-seat-config,.v-card.custom-card-map-section{
  border: 1px solid #e2e2e2;
  border-radius: 10px;
  box-shadow: none !important;
}
.inner-custom-card-seat-config{
  border: 1px solid #e2e2e2;
  border-radius: 10px;
}
.card-title-heading{
  border-bottom: 1px solid #e2e2e2;
}
.card-title-heading.ticket-card{
  background-color: #112A46;
  color: #fff;
}
.seat-box {
  width: 24px;
  height: 24px;
  flex-shrink: 0;

  display: flex;
  align-items: center;
  justify-content: center;

  background-color: #708090;      /* Use -color for clarity */
  border: 1px solid #ccc;
  border-radius: 50%;

  color: #fff;
  cursor: pointer;

  font-family: 'Inter', sans-serif;
  font-size: 8px;
  font-weight: 500;
  line-height: 1;
  font-style: normal;

  user-select: none;              /* Optional: prevent text highlight */
  transition: background-color 0.2s ease; /* Optional: smoother interaction */
}
.seat-box.selected {
  background-color: #5CC5D4;
  border-color: #0097a7;
}
.seat-box.assigned {
  background-color: #4CAF50;
  border-color: #29872c;
}

.seat-box.unassigned {
  background-color: #708090;
  border-color: #617487;
}
.seat-box.blocked {
  background-color: #B0B0B0;
  border-color: #8c8c8c;
}
.seat-box.booked {
  background-color: #E50000;
  border-color: #f15f5f;
}
span.meta-desc {
  display: block;
  line-height: 1.4;
  font-size: 12px;
}
.action-text label {
  border: 1px solid #dcdcdc;
  padding: 8px 8px;
  margin: 2px;
  text-decoration: none;
  border-radius: 4px;
  min-width: 80px;
  text-align: center;
  font-weight: 500;

  display: inline-flex;
  align-items: center;
  gap: 3px;
  font-size: 12px;
}
.action-text label.text-red{
  color:#E50000;
  border-color: #E50000;
}
.action-text label:hover{
  cursor: pointer;
  opacity: 0.8;
}
.total-span{
  align-content: center;
  font-weight: 600;
}
.seat-legends{
  font-size: 12px;
}
.selected-ticket-group{
  margin-bottom: 4px;
  font-size: 12px;
}
.selected-ticket-group span.seat{
  border: 1px solid #ccc;
  padding: 1px 6px;
  margin-right: 3px;
}

.selected-ticket-group button.remove-seat {
  color: #9a9a9a;
  padding-left: 8px;
}
.selected-ticket-group button.remove-seat:hover{
  color:red;
  text-decoration: underline;
}
.section-list{
  max-height: 500px;
  overflow-y: auto;
}
</style>