<template>
    <div class="product-box">
        <div class="product-image"  @click="addToCart">
            <img :src="product_image" />
        </div>
        <div class="product-meta">
            <div class="product-title-container">
                <div class="product-title">{{ name }}
                    <span class="display-block" v-if="enable_retail_inventory">
                        <v-tooltip bottom v-if="checkReadPermission($modules.general.venue_outlet_management.slug) && getOutletNames()">
                            <template v-slot:activator="{ on, attrs }">
                                <span v-bind="attrs" v-on="on" class="stock-with-tooltip">
                                    Remaining Stock: {{ selectedVariant?selectedVariant.stock_level: stock_level}}
                                </span>
                            </template>
                            <span>{{ getOutletLabel() }}: {{ getOutletNames() }}</span>
                        </v-tooltip>
                        <span v-else>
                            Remaining Stock: {{ selectedVariant?selectedVariant.stock_level: stock_level}}
                        </span>
                    </span>
                </div>
<!--          commenting for future testing    -->
<!--                <div class="barcode-container">
                    <img
                        v-if="selectedVariant && selectedVariant.barcode"
                        :src="selectedVariant.barcode"
                        class="barcode-image"
                        :title="`Variant Barcode: ${selectedVariant.sku || 'N/A'}`"
                        @error="onBarcodeError"
                    />
                    <img
                        v-else-if="barcode"
                        :src="barcode"
                        class="barcode-image"
                        :title="`Product Barcode: ${sku || 'N/A'}`"
                        @error="onBarcodeError"
                    />
                    <div
                        v-else
                        class="barcode-placeholder"
                        :title="'No barcode available'"
                    >
                        <v-icon small>mdi-barcode</v-icon>
                    </div>
                </div>-->
            </div>
            <div class="product-price main-color">
                <span class="price">{{ Number(selectedVariant? selectedVariant.total_price:total_price) | toCurrency }}</span>
                <span class="p-variant" v-if="filteredVariants && filteredVariants.length > 0">
                    <v-select
                        v-model="selectedVariant"
                        :items="filteredVariants"
                        item-text="attribute_name"
                        item-value="id"
                        label="Select"
                        persistent-hint
                        return-object
                        single-line
                        hide-details="auto"
                        dense
                        :menu-props="{ bottom: true, offsetY: true }"
                        background-color="#fff"
                        required
                        :rules="[(v) => !!v || 'Product variant is required']"
                    ></v-select>
                </span>

            </div>
        </div>
    </div>
</template>
<script>
export default{
    name: 'PosProductItem',
    props: {
        id: { type: Number, default: null },
        name: { type: String, default: "" },
        image: { type: String, default: "" },
        product_image: { type: String, default: "" },
        total_price: { type: [Number, String], default: 0 },
        variants:{ type: Array, default: () => [],},
        stock_level:{ type: Number, default: 0 },
        enable_retail_inventory:{ type: Number, default: 0 },
        refreshProductItem: { type: Number, default: 0},
        sku: { type: String, default: "" },
        barcode: { type: String, default: "" },
        available_outlets: { type: Array, default: () => [] },
        selectedOutlets: { type: Array, default: () => [] },
    },
    mounted() {
        this.setDefaultVariant();
    },
    watch: {
        refreshProductItem: {
            immediate: true,
            handler() {
                this.setDefaultVariant();
            },
        },
        filteredVariants: {
            immediate: true,
            handler() {
                this.setDefaultVariant();
            },
        },
        selectedOutlets: {
            immediate: true,
            handler() {
                this.setDefaultVariant();
            },
        },
    },
    data() {
        return {
            selectedVariant: null,
            variant_stock_level: 0,
        }
    },

    computed: {
        filteredVariants() {
            // If no outlet filter is selected, return all variants
            if (!this.selectedOutlets || this.selectedOutlets.length === 0) {
                return this.variants;
            }

            // If outlet filter is selected, only show variants available in those outlets
            return this.variants.filter(variant => {
                if (!variant.available_outlets || variant.available_outlets.length === 0) {
                    return false; // Variant has no outlet assignments
                }

                // Check if variant is available in any of the selected outlets
                return variant.available_outlets.some(outlet =>
                    this.selectedOutlets.includes(outlet.outlet_id)
                );
            });
        }
    },

    methods: {
        addToCart() {
            if (this.selectedVariant && this.selectedVariant.id) {
                this.$emit("addToCart", { product_id: this.id,vp_id:this.selectedVariant.id });
            } else {
                this.$emit("addToCart", { product_id: this.id,vp_id: null });
            }
        },

        setDefaultVariant() {
            this.selectedVariant = null;
            if (this.filteredVariants && this.filteredVariants.length > 0) {
                this.selectedVariant = {...this.filteredVariants[0]};
            }
        },

        onBarcodeError(event) {
            //console.error('Barcode image failed to load:', event.target.src);
            event.target.style.display = 'none';
        },

        getOutletNames() {
            let outletsToShow = [];

            // variant's outlets
            if (this.selectedVariant && this.selectedVariant.available_outlets) {
                outletsToShow = this.selectedVariant.available_outlets;
            }
            // main product outlets
            else if (this.available_outlets && this.available_outlets.length > 0) {
                outletsToShow = this.available_outlets;
            }

            if (!outletsToShow || outletsToShow.length === 0) {
                return null;
            }

            return outletsToShow.map(outlet => outlet.outlet_name || outlet.name).join(', ');
        },

        getOutletLabel() {
            let outletsToShow = [];

            // variant's outlets
            if (this.selectedVariant && this.selectedVariant.available_outlets) {
                outletsToShow = this.selectedVariant.available_outlets;
            }
            // main product outlets
            else if (this.available_outlets && this.available_outlets.length > 0) {
                outletsToShow = this.available_outlets;
            }

            return outletsToShow && outletsToShow.length === 1 ? 'Outlet' : 'Outlets';
        },

    },
}
</script>

<style scoped lang="scss">
.price{
  font-size: 0.75rem;
}

.product-title-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 8px;
}

.product-title {
  flex: 1;
}


.barcode-image {
  max-width: 120px;
  width: 120px!important;
  height: 40px;
}
.barcode-image:hover {
  border-color: #4FAEAF;
  box-shadow: 0 1px 3px rgba(79, 174, 175, 0.2);
}

.barcode-placeholder {
  width: 100%;
  height: 30px;
  border: 1px dashed #ccc;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
  color: #999;
}

.barcode-placeholder:hover {
  border-color: #999;
}

.stock-with-tooltip {
    cursor: help;
    border-bottom: 1px dotted #999;
}
</style>
