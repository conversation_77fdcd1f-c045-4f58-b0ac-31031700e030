<script>

import DateOfBirthField from '@/components/Fields/DateOfBirthField.vue'
import ImageUploader from '@/components/Image/ImageUploader.vue'

export default {
  components: { ImageUploader, DateOfBirthField },
  props: {
    sellOfferModal: {
      type: Boolean,
      default: false
    },
    offers: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      valid:true,
      form: {
        offer_id: null,
        age_group: null,
        country_id: null,
        customer_id: null,
        customer_tag: null,
        dob: null,
        email: null,
        first_name: null,
        gender: null,
        image_path: null,
        last_name: null,
        mobile: null,
        name: null,
        nameSearch: null
      }
    }
  },
  mounted () {
    if (this.$store.getters.getCustomerAgeRangeConfiguration.status == false) {
      this.$store.dispatch('LoadCustomerAgeRangeConfiguration')
    }
    if (this.$store.getters.getCustomerAgeRange.status == false) {
      this.$store.dispatch('LoadCustomerAgeRange')
    }
  },
  computed: {
    modalOpen: {
      get () {
        return this.sellOfferModal
      },
      set (val) {
        this.$emit('close', val)
      }
    },
    customerAgeRange () {
      return this.$store.getters.getCustomerAgeRangeConfiguration.data
    },
    ageRanges () {
      return this.$store.getters.getCustomerAgeRange.data
    },
    countries () {
      return this.$store.getters.getCountries.data
    },
  },
  methods: {
    setCustomerData (data) {
      if (!data.customer_id) {
        this.$set(this.form, 'customer_id', null)
      }

      if (!data.name && data.first_name) {
        this.$set(this.form, 'name', data.first_name)
      }

      if (
          this.form.customer_id &&
          !data.customer_id &&
          this.form.name != data.name &&
          this.form.mobile != data.mobile
      )
      {
        this.$set(this.form, 'mobile', null)
        this.form.search = null
        this.form.nameSearch = null
        this.$set(this.form, 'email', null)
        this.$set(this.form, 'gender', null)
        this.$set(this.form, 'name', null)
        this.$set(this.form, 'customer_id', null)
        this.$set(this.form, 'first_name', null)
        this.$set(this.form, 'image_path', null)
        this.$set(this.form, 'dob', null)
        this.$set(this.form, 'age_group', null)
        this.$set(this.form, 'country_id', null)
        this.$set(this.form, 'last_name', null)
        this.$set(this.form, 'customer_tag', null)
        this.$set(this.form, 'opt_marketing', false)
        this.$forceUpdate()
      }

      if (data.mobile) this.$set(this.form, 'mobile', data.mobile)
      if (data.email) this.$set(this.form, 'email', data.email)
      if (data.country_id) {
        this.$set(this.form, 'country_id', data.country_id)
      } else {
        this.$set(this.form, 'country_id', null)
      }
      if (data.gender) {
        this.$set(this.form, 'gender', data.gender)
      } else {
        this.$set(this.form, 'gender', null)
      }
      if (data.dob) {
        this.$set(this.form, 'dob', data.dob)
      } else {
        this.$set(this.form, 'dob', null)
      }
      if (data.age_group) {
        this.$set(this.form, 'age_group', data.age_group)
      } else {
        this.$set(this.form, 'age_group', null)
      }
      if (data.customer_tag) {
        this.$set(this.form, 'customer_tag', data.customer_tag)
      } else {
        this.$set(this.form, 'customer_tag', null)
      }
      if (data.name) this.$set(this.form, 'name', data.name)
      if (data.last_name) {
        this.$set(this.form, 'last_name', data.last_name)
      } else {
        this.$set(this.form, 'last_name', null)
      }
      if (data.first_name)
        this.$set(this.form, 'first_name', data.first_name)
      if (data.customer_id)
        this.$set(this.form, 'customer_id', data.customer_id)
      if (data.image_path) {
        this.$set(this.form, 'image_path', data.image_path)
      } else {
        this.$set(this.form, 'image_path', null)
      }
      if (data.opt_marketing) {
        if (data.opt_marketing == 1) {
          this.$set(this.form, 'opt_marketing', true)
        } else {
          this.$set(this.form, 'opt_marketing', false)
        }
      }
      this.$forceUpdate()
    },
    setCardData (data) {
      if (!data.customer_id) {
        this.$set(this.form, 'customer_id', null)
      }

      if (!data.name && data.first_name) {
        this.$set(this.form, 'name', data.first_name)
      }

      if (!data.customer_id && this.form.name != data.name) {
        this.$set(this.form, 'mobile', null)
        this.form.search = null
        this.form.nameSearch = null
        this.$set(this.form, 'email', null)
        this.$set(this.form, 'gender', null)
        this.$set(this.form, 'name', null)
        this.$set(this.form, 'customer_id', null)
        this.$set(this.form, 'first_name', null)
        this.$set(this.form, 'image_path', null)
        this.$set(this.form, 'dob', null)
        this.$set(this.form, 'age_group', null)
        this.$set(this.form, 'country_id', null)
        this.$set(this.form, 'last_name', null)
        this.$set(this.form, 'opt_marketing', false)
        this.$forceUpdate()
      }

      if (data.mobile) {
        this.$set(this.form, 'mobile', data.mobile)
      }
      if (data.email) this.$set(this.form, 'email', data.email)
      if (data.country_id) {
        this.$set(this.form, 'country_id', data.country_id)
      } else {
        this.$set(this.form, 'country_id', null)
      }
      if (data.country_id) {
        this.$set(this.form, 'id_proof_type_id', data.id_proof_type_id)
      }

      if (data.id_proof_number) {
        this.$set(this.form, 'id_proof_number', data.id_proof_number)
      }

      if (data.gender) {
        this.$set(this.form, 'gender', data.gender)
      } else {
        this.$set(this.form, 'gender', null)
      }
      if (data.dob) {
        this.$set(this.form, 'dob', data.dob)
      } else {
        this.$set(this.form, 'dob', null)
      }
      if (data.age_group) {
        this.$set(this.form, 'age_group', data.age_group)
      } else {
        this.$set(this.form, 'age_group', null)
      }

      if (data.image) {
        this.$set(this.form, 'image', data.image)
      }

      if (data.name) this.$set(this.form, 'name', data.name)
      if (data.last_name) {
        this.$set(this.form, 'last_name', data.last_name)
      } else {
        this.$set(this.form, 'last_name', null)
      }
      if (data.first_name)
        this.$set(this.form, 'first_name', data.first_name)
      if (data.customer_id)
        this.$set(this.form, 'customer_id', data.customer_id)
      if (data.image_path) {
        this.$set(this.form, 'image_path', data.image_path)
      } else {
        this.$set(this.form, 'image_path', null)
      }
      if (data.opt_marketing) {
        if (data.opt_marketing == 1) {
          this.$set(this.form, 'opt_marketing', true)
        } else {
          this.$set(this.form, 'opt_marketing', false)
        }
      }
      this.$forceUpdate()
    },
    purchaseOffer () {
      if (!this.$refs.form.validate()) {
        this.showError('Please fill all the required fields')
        return;
      }
      this.showLoader("Purchasing...")
      this.$http.post('venues/offers/packages/sell-package', this.form).then((response) => {
        if (response && response.status == 200) {
          const data = response.data.data;
          this.$emit('close')
          this.$emit('showReceipt',data.id);
          this.showSuccess('Package purchased successfully')
        }
      }).catch((error) => {
        this.errorChecker(error);
      }).finally(() => {
        this.hideLoader()
      })
    }
  }
}
</script>

<template>
  <v-dialog
      v-model="modalOpen"
      max-width="800"
      persistent
      @input="$emit('close')"
  >

    <v-card>
      <v-form ref="form" v-model="valid">
        <v-card-title class="border-bottom">
          <div class="w-full">
            Sell Package
          </div>
        </v-card-title>
        <v-card-text class="mt-4">
          <div class="max-20">
            <label for="">Packages</label>
            <v-select
                v-model="form.offer_id"
                :items="offers"
                :menu-props="{ bottom: true, offsetY: true }"
                :rules="[(v) => !!v || 'Package is required']"
                background-color="#fff"
                class="q-autocomplete shadow-0"
                dense
                hide-details="auto"
                item-text="name"
                item-value="id"
                outlined
            />
          </div>

          <div class="font-semibold text-base text-blue mt-4">Customer details</div>

          <div class="bordered shadow-0 mt-2 px-4">
            <v-row class="mb-4" dense>
              <v-col md="12">
                <div class="d-flex justify-space-between">
                  <div class="d-flex">
                    <card-reader-button
                        className=" mt-5 text-blue text-capitalize light-blue-color"

                        docType="chip"
                        label="Samsotech Reader"

                        @data="
                          (data) => {
                            setCardData(data);
                          }
                        "
                    ></card-reader-button>
                    <card-data-button
                        className="mt-5 ml-4 text-blue text-capitalize light-blue-color"
                        label="HID Omnikey"
                        @data="
                          (data) => {
                            setCardData(data);
                          }
                        "
                    ></card-data-button>
                  </div>


                </div>
              </v-col>


            </v-row>

            <v-row align="center" dense>
              <v-col md="3">
                <div style="aspect-ratio: 1/1 ">
                  <image-uploader
                      :height="240"
                      :image_path="form.image_path"
                      :show-guide="false"
                      defaultImage="user"
                      message-text=""
                      text=""
                      @remove="
                          () => {
                            form.image = null;
                          }
                        "
                      @upload="
                          (data) => {
                             form.image = data;
                          }
                        "
                  ></image-uploader>
                </div>
              </v-col>
              <v-col md="9">
                <v-row dense>
                  <v-col md="12">
                    <label>Mobile Number*</label>
                    <v-mobile-search
                        ref="mobile"
                        v-model="form.search"
                        :dense="true"
                        :dial-code-grid-size="3"
                        :productTypeId="4"
                        :selected="form.mobile"
                        :show-label="false"
                        background-color=""
                        class-name1="q-text-field shadow-0"
                        class-name2="q-text-field shadow-0 mobile_auto_complete_hide_anchor"
                        hide-details="auto"
                        label=""
                        @updateCustomer="setCustomerData($event)"
                    ></v-mobile-search>
                  </v-col>

                  <v-col md="12">
                    <label>Customer Name*</label>
                    <v-name-search
                        v-model="form.nameSearch"
                        :dense="true"
                        :email="form.email"
                        :mobile="form.mobile"
                        :productTypeId="4"
                        :selected="form.name"
                        class-name="q-text-field shadow-0"
                        hide-details="auto"
                        label=""
                        @updateCustomer="setCustomerData($event)"

                    ></v-name-search>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <v-row dense>


              <v-col cols="12" md="6" sm="6">
                <label>Email*</label>

                <v-text-field
                    v-model="form.email"
                    :rules="[
                      (v) => !!v || 'E-mail is required',
                      (v) => /.+@.+\..+/.test(v) || 'E-mail must be valid',
                    ]"
                    background-color="#fff"
                    class="q-text-field shadow-0"
                    dense
                    hide-details="auto"
                    outlined
                    required
                    validate-on-blur
                ></v-text-field>
              </v-col>
              <v-col v-if="customerAgeRange" md="6">
                <label>Age Group</label>
                <v-select
                    v-if="customerAgeRange"
                    v-model="form.age_group"
                    :items="ageRanges"
                    :menu-props="{ bottom: true, offsetY: true }"
                    background-color="#fff"
                    class="q-autocomplete shadow-0"
                    dense
                    hide-details="auto"
                    item-text="name"
                    item-value="id"
                    outlined
                    validate-on-blur
                ></v-select>
              </v-col>
              <v-col v-else md="6">
                <label>Date Of Birth</label>
                <date-of-birth-field v-model="form.dob" :dense="true"></date-of-birth-field>
              </v-col>
              <v-col cols="12" md="6" sm="6">
                <label>Nationality</label>

                <v-autocomplete
                    v-model="form.country_id"
                    :items="countries"
                    background-color="#fff"
                    class="q-autocomplete shadow-0"
                    dense
                    hide-details="auto"
                    item-text="name"
                    item-value="id"
                    outlined
                    validate-on-blur
                ></v-autocomplete>
              </v-col>
              <v-col cols="12" md="6" sm="6">
                <label>Gender</label>
                <v-select
                    v-model="form.gender"
                    :items="['Male', 'Female']"
                    :menu-props="{ bottom: true, offsetY: true }"
                    background-color="#fff"
                    class="q-autocomplete shadow-0"
                    dense
                    hide-details="auto"
                    outlined
                    validate-on-blur
                ></v-select>
              </v-col>
            </v-row>
            <v-row dense>
              <v-col md="12">
                <div class="d-flex gap-x-5">
                  <span class="d-flex align-center"><v-checkbox v-model="form.opt_marketing" :ripple="false"/> Opt In Marketing</span>
                </div>
              </v-col>
            </v-row>
          </div>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
                class="ma-2"
                text
                @click="modalOpen = false"
            >
              Close
            </v-btn>
            <v-btn
                class="ma-2 white--text blue-color"
                color="darken-1"
                @click="purchaseOffer"
            >
              Save
            </v-btn>
          </v-card-actions>
        </v-card-text>
      </v-form>
    </v-card>

  </v-dialog>
</template>

<style scoped>

</style>
