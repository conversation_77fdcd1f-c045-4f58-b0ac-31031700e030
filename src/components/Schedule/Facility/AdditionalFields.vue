<template>
  <v-col cols="12" :md="col.md" v-if="fieldConfig.is_visible">
    <!-- Label -->
    <label class="field-label">
      {{ fieldConfig.name }}<span v-if="fieldConfig.is_required" class="required">*</span>
    </label>

    <!-- Text Input -->
    <v-text-field
        v-if="fieldConfig.type === 'text_box'"
        v-model="localValue"
        :rules="validationRules"
        background-color="#fff"
        class="q-text-field shadow-0 text_field1"
        dense
        outlined
        validate-on-blur
        hide-details="auto"
    />

    <!-- Textarea -->
    <v-textarea
        v-else-if="fieldConfig.type === 'text_area'"
        v-model="localValue"
        :rules="validationRules"
        class="q-text-field shadow-0 text_field1"
        dense
        rows="3"
        outlined
        validate-on-blur
        hide-details="auto"
    />

    <!-- Checkboxes -->
    <div v-else-if="fieldConfig.type === 'check_boxes'" class="d-flex gap-x-2">
      <v-checkbox
          v-for="option in fieldConfig.options"
          :key="option.value"
          :label="option.value"
          :value="option.value"
          v-model="selectedOptions"
          multiple
          hide-details
          @change="handleCheckboxChange"
      />
    </div>

    <!-- Dropdown -->
    <v-select
        v-else-if="fieldConfig.type === 'drop_down'"
        v-model="localValue"
        :rules="validationRules"
        :items="fieldConfig.options"
        :multiple="fieldConfig.multiple"
        item-text="value"
        item-value="value"
        background-color="#fff"
        class="q-autocomplete shadow-0"
        dense
        outlined
        validate-on-blur
        hide-details="auto"
    />

    <!-- Radio Buttons -->
    <v-radio-group
        v-else-if="fieldConfig.type === 'radio_buttons'"
        v-model="localValue"
        :rules="validationRules"
        row
        class="d-flex gap-x-2"
    >
      <v-radio
          class="r_btn"
          v-for="option in fieldConfig.options"
          :key="option.value"
          :label="option.value"
          :value="option.value"
      />
    </v-radio-group>

    <date-input-field
        v-else-if="fieldConfig.type === 'date'"
        v-model="localValue"
        :rules="validationRules"
        :dense="true"
        label=""
        background-color="#fff"
        hide-details
        class="q-autocomplete shadow-0"
    />
    <!-- Date Picker -->

    <mobile-number-field
        v-else-if="fieldConfig.type === 'phone_number'"
        v-model="localValue"
        :rules="validationRules"
        :dense="true"
        :outlined="true"
        :variant="1"
        :required="!!fieldConfig.is_required"
    />
    <!-- Disclaimer Form with Signature -->
    <div v-else-if="fieldConfig.type === 'disclaimer_form' || fieldConfig.field_type === 'disclaimer_form'">
      <!-- Hidden file input for signature -->
      <input
        type="file"
        ref="hiddenSignatureInput"
        style="display: none;"
        accept="image/*"
        @change="handleHiddenFileChange"
      />

      <!-- Signature Preview and Button Row -->
      <v-row class="mx-0">
        <v-col md="10" class="pl-0 pr-1">
          <!-- Signature Preview or Placeholder -->
          <div v-if="displaySignatureUrl" class="signature-preview-container">
            <div class="signature-preview-wrapper">
              <img
                :src="displaySignatureUrl"
                alt="Signature"
                class="signature-preview-image"
              />
            </div>
          </div>
          <div v-else class="signature-placeholder" style="height: 40px">
            <v-icon color="grey">mdi-draw</v-icon>
            <span class="grey--text ml-2">No signature added</span>
          </div>
        </v-col>
        <v-col align-self="end" md="2" class="justify-center px-0">
          <v-btn
            class="white--text q-text-field shadow-0 bordered"
            color="blue-grey"
            height="40"
            width="40"
            min-width="40"
            large
            outlined
            style="background-color: #fff"
            @click="signatureDialog = true"
          >
            <v-icon color="blue-grey">mdi-draw</v-icon>
          </v-btn>
        </v-col>
      </v-row>
    </div>

    <!-- File Input -->
    <div v-else-if="fieldConfig.type === 'file'">
<!--      <v-row v-if="showSignatureButton" class="mx-0 signature-button-row">-->
<!--        <v-col md="10" class="pl-0 pr-1">-->
<!--          <v-file-input-->
<!--              v-model="safeFileInputValue"-->
<!--              background-color="#fff"-->
<!--              class="q-text-field shadow-0"-->
<!--              dense-->
<!--              hide-details="auto"-->
<!--              outlined-->
<!--              prepend-icon-->
<!--              prepend-inner-icon="mdi-paperclip"-->
<!--              :rules="validationRules"-->
<!--              clearable-->
<!--              style="min-height: 40px;"-->
<!--          >-->
<!--            <template v-slot:selection>-->
<!--              <span v-if="safeFileInputValue && safeFileInputValue.length > 0">-->
<!--                {{ safeFileInputValue[0].name }}-->
<!--              </span>-->
<!--              <span v-else-if="localValue instanceof File">-->
<!--                {{ localValue.name }}-->
<!--              </span>-->
<!--              <span v-else-if="typeof localValue === 'string' && localValue">-->
<!--                {{ localValue.split('/').pop() }}-->
<!--              </span>-->
<!--              <span v-else class="grey&#45;&#45;text">-->
<!--                No file selected-->
<!--              </span>-->
<!--            </template>-->
<!--          >-->
<!--            <template v-slot:prepend-inner v-if="localValue">-->
<!--              <v-tooltip bottom>-->
<!--                <template v-slot:activator="{ on }">-->
<!--                  <v-icon-->
<!--                      color="cyan"-->
<!--                      v-if="typeof localValue === 'string'"-->
<!--                      @click="openFile(localValue)"-->
<!--                      v-on="on"-->
<!--                  >-->
<!--                    mdi-download-box-->
<!--                  </v-icon>-->
<!--                  <v-icon v-else v-on="on">mdi-card-account-details</v-icon>-->
<!--                </template>-->
<!--                <span v-if="typeof localValue === 'string'">Download file</span>-->
<!--                <span v-else>Upload File</span>-->
<!--              </v-tooltip>-->
<!--            </template>-->
<!--          </v-file-input>-->
<!--        </v-col>-->
<!--&lt;!&ndash;        <v-col align-self="end" md="2" class="justify-center px-0">&ndash;&gt;-->
<!--&lt;!&ndash;          <v-btn&ndash;&gt;-->
<!--&lt;!&ndash;              class="white&#45;&#45;text q-text-field shadow-0 bordered"&ndash;&gt;-->
<!--&lt;!&ndash;              color="blue-grey"&ndash;&gt;-->
<!--&lt;!&ndash;              height="40"&ndash;&gt;-->
<!--&lt;!&ndash;              width="40"&ndash;&gt;-->
<!--&lt;!&ndash;              min-width="40"&ndash;&gt;-->
<!--&lt;!&ndash;              large&ndash;&gt;-->
<!--&lt;!&ndash;              outlined&ndash;&gt;-->
<!--&lt;!&ndash;              style="background: #00b0af"&ndash;&gt;-->
<!--&lt;!&ndash;              @click="$emit('openSignatureDialog')"&ndash;&gt;-->
<!--&lt;!&ndash;          >&ndash;&gt;-->
<!--&lt;!&ndash;            <v-icon color="blue-grey">mdi-draw</v-icon>&ndash;&gt;-->
<!--&lt;!&ndash;          </v-btn>&ndash;&gt;-->
<!--&lt;!&ndash;        </v-col>&ndash;&gt;-->
<!--      </v-row>-->

      <!-- Regular file input (when no signature button) -->
      <v-file-input
          v-model="safeFileInputValue"
          background-color="#fff"
          class="q-text-field shadow-0"
          dense
          hide-details="auto"
          outlined
          prepend-icon
          prepend-inner-icon="mdi-paperclip"
          :rules="validationRules"
      >
        <template v-slot:prepend-inner v-if="localValue">
          <v-tooltip bottom>
            <template v-slot:activator="{ on }">
              <v-icon
                  color="cyan"
                  v-if="typeof localValue === 'string'"
                  @click="openFile(localValue)"
                  v-on="on"
              >
                mdi-download-box
              </v-icon>
              <v-icon v-else v-on="on">mdi-card-account-details</v-icon>
            </template>
            <span v-if="typeof localValue === 'string'">Download file</span>
            <span v-else>Upload File</span>
          </v-tooltip>
        </template>
      </v-file-input>
    </div>



    <!-- Email Input -->
    <v-text-field
        v-else-if="fieldConfig.type === 'email'"
        v-model="localValue"
        :rules="validationRules"
        :placeholder="`Email${fieldConfig.is_required ? ' *' : ''}`"
        background-color="#fff"
        class="q-text-field shadow-0"
        dense
        label=""
        outlined
        validate-on-blur
        hide-details="auto"
    />


    <DigitalSignatureModal :signature-dialog="signatureDialog" @setSignatureDialog="val => signatureDialog = val" v-model="localValue" @signatureSaved="generateBlob" />

  </v-col>



</template>

<script>
import MobileNumberField from "@/components/Fields/MobileNumberField.vue";
import DateInputField from "@/components/Fields/DateInputField.vue";
import common from "@/mixins/common";
import DigitalSignatureModal from "@/components/Fields/DigitalSignatureModal.vue";

export default {
  components:{DigitalSignatureModal, DateInputField, MobileNumberField},
  mixins: [common],
  name: 'DynamicField',
  props: {
    value: {
      type: Object,
      default: () => ({ value: null })
    },
    modelValue: {
      type: Object,
      default: () => ({ value: null })
    },
    fieldConfig: {
      type: Object,
      required: true,
      default: () => ({
        id: null,
        slug: '',
        is_required: false,
        is_visible: true,
        type: null,
        name: '',
        options: [],
        multiple: false
      })
    },
    col:{
      md:6,
      sm:12
    },
    showSignatureButton: {
      type: Boolean,
      default: false
    }
  },
  emits: ['input', 'update:modelValue', 'openSignatureDialog'],
  data() {
    return {
      selectedOptions: [],
      signaturePreview: null,
      signatureFileName: null,
      currentSignatureFile: null,
      signatureDialog:false,
    };
  },
  computed: {
    localValue: {
      get() {
        if (this.fieldConfig.type === 'check_boxes') {
          return this.selectedOptions;
        }
        return this.modelValue?.value ?? this.value?.value;
      },
      set(val) {
        console.log("setting local value")
        if (this.fieldConfig.type === 'check_boxes') {
          this.selectedOptions = Array.isArray(val) ? [...val] : [];
          this.emitUpdate();
        } else {
          this.emitUpdate(val);
        }
      }
    },
    existingSignatureUrl() {
      // Check if we have an existing signature URL (string value from API)
      const fieldType = this.fieldConfig.type || this.fieldConfig.field_type;
      if (fieldType === 'disclaimer_form') {

        // Try different ways to access the value based on data structure
        let value = null;

        // Method 1: Direct value access (for new File objects)
        if (this.modelValue?.value) {
          value = this.modelValue.value;
        } else if (this.value?.value) {
          value = this.value.value;
        }
        // Method 2: Direct access (for mapped field structures)
        else if (this.modelValue && typeof this.modelValue === 'string') {
          value = this.modelValue;
        } else if (this.value && typeof this.value === 'string') {
          value = this.value;
        }
        // Method 3: Check if the entire object is the value (for some data structures)
        else if (this.modelValue && typeof this.modelValue === 'object' && this.modelValue.value) {
          value = this.modelValue.value;
        }


        // If value is a File object (new signature), don't show existing URL
        if (value instanceof File) {
          return null;
        }

        if (typeof value === 'string' && value && !value.startsWith('blob:')) {
          return this.s3BucketURL + value;
        }

        console.log('NO VALID STRING VALUE FOUND');
      }
      return null;
    },

    displaySignatureUrl() {
      const fieldType = this.fieldConfig.type || this.fieldConfig.field_type;
      if (fieldType === 'disclaimer_form') {
        // Prioritize new signature preview over existing signature
        if (this.signaturePreview) {
          return this.signaturePreview;
        } else if (this.existingSignatureUrl) {
          return this.existingSignatureUrl;
        }
      }
      return null;
    },
    validationRules() {
      const rules = [];
      const fieldType = this.fieldConfig.type || this.fieldConfig.field_type;

      if (this.fieldConfig.is_required) {
        rules.push(value => {
          // Handle file fields (including existing file paths)
          if (
              this.fieldConfig.type === 'file' &&
              typeof this.localValue === 'string' &&
              this.localValue !== ''
          ) {
            return true;
          }

          // Handle disclaimer_form fields (signature fields)
          if (fieldType === 'disclaimer_form') {
            // Valid if we have an existing signature URL or a new file
            if (this.existingSignatureUrl || (value instanceof File)) {
              return true;
            }
            return `${this.fieldConfig.name} is required`;
          }

          if (Array.isArray(value)) {
            return value.length > 0 || `${this.fieldConfig.name} is required`;
          }
          return (!!value || value === 0) || `${this.fieldConfig.name} is required`;
        });
      }

      if (this.fieldConfig.type === 'email') {
        rules.push(v => !v || /.+@.+\..+/.test(v) || 'E-mail must be valid');
      }
      return rules;
    },

    safeFileInputValue: {
      get() {
        // If localValue is a string (existing file path), return null to show as empty
        if (typeof this.localValue === 'string') {
          return null;
        }
        // If localValue is a File object, convert it to FileList for v-file-input
        if (this.localValue instanceof File) {
          const dataTransfer = new DataTransfer();
          dataTransfer.items.add(this.localValue);
          return dataTransfer.files;
        }
        // Handle FileList objects (from regular file upload)
        return this.localValue;
      },
      set(val) {
        this.localValue = val;
      }
    }
  },
  watch: {
    value(newVal) {
      if (this.fieldConfig.type === 'check_boxes') {
        this.selectedOptions = Array.isArray(newVal?.value) ? [...newVal.value] : [];
      }
    },
    modelValue(newVal) {
      if (this.fieldConfig.type === 'check_boxes') {
        this.selectedOptions = Array.isArray(newVal?.value) ? [...newVal.value] : [];
      }

      // Handle signature files
      const fieldType = this.fieldConfig.type || this.fieldConfig.field_type;
      if (fieldType === 'disclaimer_form' && newVal?.value instanceof File) {
        this.updateSignatureFile(newVal.value);
      }
    },



  },
  mounted() {
    this.initSelectedOptions();
    this.initExistingSignature();

    // Check if we have an initial signature file
    const fieldType = this.fieldConfig.type || this.fieldConfig.field_type;
    if (fieldType === 'disclaimer_form') {
      if (this.modelValue?.value instanceof File) {
        this.updateSignatureFile(this.modelValue.value);
      }
    }
  },
  methods: {
    generateBlob(file){
      this.signaturePreview = URL.createObjectURL(file);
    },
    emitUpdate(newValue = this.selectedOptions) {
      // Send the complete field structure with all properties
      const payload = {
        ...this.fieldConfig,
        value: newValue
      };
      console.log('payload')
      console.log(payload)
      this.$emit('input', payload);
      this.$emit('update:modelValue', payload);
    },
    initSelectedOptions() {
      if (this.fieldConfig.type === 'check_boxes') {
        const initial = this.modelValue?.value || this.value?.value || [];
        this.selectedOptions = Array.isArray(initial) ? [...initial] : [];
      }
    },

    initExistingSignature() {
      const fieldType = this.fieldConfig.type || this.fieldConfig.field_type;
      if (fieldType === 'disclaimer_form' && this.existingSignatureUrl) {
        this.signaturePreview = null;
        this.signatureFileName = null;
      }
    },
    handleCheckboxChange() {
      this.emitUpdate();
    },
    handleHiddenFileChange(event) {
      const file = event.target.files[0];
      if (file) {
        this.setSignatureFile(file);
      }
    },
    setSignatureFile(file) {
      // Set the file as the value
      this.localValue = file;
      console.log(file);
      console.log('setting');

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        this.signaturePreview = e.target.result;
        this.signatureFileName = file.name;
      };
      reader.readAsDataURL(file);

    },

    openFile(filePath) {
      // Handle file download for regular file fields (not signatures)
      if (filePath && typeof filePath === 'string') {
        const fullUrl = filePath.startsWith('http') ? filePath : this.s3BucketURL + filePath;
        window.open(fullUrl, '_blank');
      }
    },

    downloadSignature() {
      if (this.existingSignatureUrl) {
        const link = document.createElement('a');
        link.href = this.existingSignatureUrl;
        link.download = 'signature.png';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    },

    updateSignatureFile(file) {
      if (file instanceof File) {
        this.currentSignatureFile = file;
        const reader = new FileReader();
        reader.onload = (e) => {
          this.signaturePreview = e.target.result;
          this.signatureFileName = file.name;
        };
        reader.readAsDataURL(file);
      }
    }

  }
};
</script>

<style scoped lang="scss">
.field-label {
  //font-weight: 600;
  display: block;
  margin-bottom: 4px;
}

.signature-preview-container {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px;
  background-color: #fff;
  min-height: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.signature-preview-wrapper {
  position: relative;
  display: inline-block;
  max-width: 200px;
}

.signature-download-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  background-color: rgba(255, 255, 255, 0.9) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.signature-preview-image {
  max-width: 100%;
  max-height: 60px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.signature-status {
  margin-top: 8px;
}

.signature-filename {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.signature-placeholder {
  cursor: not-allowed;
  border: 1px dashed #ccc;
  border-radius: 4px;
  padding: 16px;
  text-align: center;
  background-color: #fafafa;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.required {
  color: red;
  margin-left: 2px;
}


::v-deep .r_btn .v-label {
  white-space: normal !important;
  overflow-wrap: break-word !important;
  word-break: break-word !important;
}

/* Signature button styling */
.signature-button-row {
  align-items: flex-end !important;
}

.signature-button-row .v-file-input {
  min-height: 40px !important;
}

.signature-button-row .v-btn {
  margin-bottom: 0 !important;
}
</style>
