<template>
  <v-dialog v-model="showEventSeatMapDialog" width="760px" scrollable persistent>
    <v-card color="lighten-4" min-width="700px" flat class="pt-0" v-if="seatMapImage">
      <v-card-title >
        <div class="row pt-1 border-bottom">
          <div class="col-md-12">
            <div class="d-flex justify-space-between align-center mt-2">
              <SvgIcon
                  class="text-xl font-semibold"
                  text="Seat Map"
                  style="color: black"
              >
              </SvgIcon>
              <v-btn fab x-small class="shadow-0" @click="$emit('close');">
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </div>
          </div>
        </div>
      </v-card-title>
      <v-card-text class="pt-0">
        <v-row class="mt-0">
          <v-col md="12">
            <v-img
                width="100%"
                height="auto"
                cover
                :src="seatMapImageSrc"
            ></v-img>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
<script>
import SvgIcon from "@/components/Image/SvgIcon.vue";
import images from "@/utils/images";
export default {
  props: {
    showEventSeatMapDialog: {
      type: Boolean,
      default: false,
    },
    seatMapImage: { type: String, default: null },
    defaultImage: { type: String, default: "default" },
  },
  computed: {
    seatMapImageSrc() {
      const url = this.seatMapImage
          ? `${this.s3BucketURL.replace(/\/?$/, '/')}${this.seatMapImage}`
          : this.images[this.defaultImage];

      console.log('Image URL:', url); // Check the full URL
      return url;
    }
  },
  data() {
    return {
      images: images,
    };
  },
  components: {
    SvgIcon,
  },
}

</script>