<template>
  <div class="v-tablet-sign" :class="{
    'v-tablet-sign--disconnected': !isConnected,
    'v-tablet-sign--compact': compact
  }">
    <!-- Header Controls -->
    <div class="v-tablet-sign__header">
      <v-btn
          @click="handleConnect"
          :disabled="isConnecting || isConnected"
          class="white--text teal-color rounded-lg"
          height="40"
          text
          :loading="isConnecting"
      >
        <span class="font-bold text-lg">
          <v-icon left>{{ isConnected ? 'mdi-check-circle' : 'mdi-tablet' }}</v-icon>
          {{ isConnecting ? 'Connecting...' : isConnected ? 'Connected' : 'Connect Tablet' }}
        </span>
      </v-btn>
    </div>

    <!-- Drawing Area -->
    <div
      class="v-tablet-sign__canvas-container"
      :class="{ 'v-tablet-sign__canvas-container--compact': compact }"
      :style="{
        width: containerWidth + 'px',
        height: containerHeight + 'px'
      }"
    >
      <!-- Signature Preview (when saved) -->
      <img
        v-if="signatureSaved && signatureDataURL"
        :src="signatureDataURL"
        class="v-tablet-sign__preview"
        alt="Saved signature"
      />

      <!-- Drawing Canvas (when connected and not saved) -->
      <template v-if="!signatureSaved && isConnected">
        <canvas
            ref="canvasRef"
            class="v-tablet-sign__canvas"
            :width="canvasWidth"
            :height="canvasHeight"
          :style="{
            transform: `scale(${scaleX}, ${scaleY})`
          }"
        />
        <svg
            ref="svgRef"
            class="v-tablet-sign__svg"
            :width="canvasWidth"
            :height="canvasHeight"
          :style="{
            transform: `scale(${scaleX}, ${scaleY})`
          }"
        />
      </template>

      <!-- Disconnected State Message -->
      <div v-if="!isConnected && !signatureSaved" class="v-tablet-sign__disconnected">
        <v-icon size="64" color="grey lighten-1">mdi-tablet</v-icon>
        <div class="text-h6 grey--text text--lighten-1 mt-2">Device Disconnected</div>
        <div class="caption grey--text text--lighten-1">Connect your tablet to start drawing</div>
      </div>
    </div>


    <!-- File Controls -->
    <div class="d-flex gap-x-2 align-center justify-center">

      <v-btn
          style="height: 46px; border: 1px solid #dcdcdc !important"
          color="teal"
          :dark="isConnected"
          :disabled="!isConnected"
          @click="handleClearScreen"
      >
        <v-icon>mdi-refresh</v-icon>
      </v-btn>


      <v-btn
          style="height: 46px; border: 1px solid #dcdcdc !important"
          color="teal"
          :dark="isConnected && hasStrokes"
          :disabled="!isConnected || !hasStrokes"
          @click="handleSaveSignature"
      >
        <v-icon>mdi-check</v-icon>
      </v-btn>

    </div>

  </div>
</template>

<script>
// Wacom STU-540 WebHID Driver Class

class WacomSTU540Driver {
  constructor() {
    if (navigator == null || navigator.hid == null) return null

    this.config = {
      chunkSize: 253,
      vid: 1386,
      pid: 168,
      imageFormat24BGR: 0x04,
      width: 400,
      height: 240,
      scaleFactor: 13.5,
      pressureFactor: 1023,
      refreshRate: 0,
      tabletWidth: 0,
      tabletHeight: 0,
      deviceName: null,
      firmware: null,
      eSerial: null,
      onPenDataCb: null,
      onHidChangeCb: null,
    }

    this.commands = {
      penData: 0x01,
      information: 0x08,
      capability: 0x09,
      writingMode: 0x0e,
      eSerial: 0x0f,
      clearScreen: 0x20,
      inkMode: 0x21,
      writeImageStart: 0x25,
      writeImageData: 0x26,
      writeImageEnd: 0x27,
      writingArea: 0x2a,
      brightness: 0x2b,
      backgroundColor: 0x2e,
      penColorAndWidth: 0x2d,
      penDataTiming: 0x34,
    }

    this.device = null
    this.imageCache = null
    this.setupHidListeners()
  }

  setupHidListeners() {
    navigator.hid.addEventListener("connect", (e) => {
      if (
          this.config.onHidChangeCb &&
          e.device.vendorId === this.config.vid &&
          e.device.productId === this.config.pid
      ) {
        this.config.onHidChangeCb("connect", e.device)
      }
    })

    navigator.hid.addEventListener("disconnect", (e) => {
      if (
          this.config.onHidChangeCb &&
          e.device.vendorId === this.config.vid &&
          e.device.productId === this.config.pid
      ) {
        this.config.onHidChangeCb("disconnect", e.device)
      }
    })
  }

  async checkDeviceAvailability() {
    if (this.isConnected()) return true
    const devices = await navigator.hid.getDevices()
    return devices.some((device) => device.vendorId === this.config.vid && device.productId === this.config.pid)
  }

  async connectToDevice() {
    if (this.isConnected()) return true

    try {
      const devices = await navigator.hid.requestDevice({
        filters: [{ vendorId: this.config.vid, productId: this.config.pid }],
      })

      if (!devices[0]) return false

      this.device = devices[0]
      await this.device.open()
      await this.initializeDevice()
      return true
    } catch (error) {
      console.error("Failed to connect to Wacom device:", error)
      return false
    }
  }

  async initializeDevice() {
    this.device.addEventListener("inputreport", this.handleInputReport.bind(this))

    const capabilityData = await this.readFeatureReport(this.commands.capability)
    this.config.tabletWidth = capabilityData.getUint16(1)
    this.config.tabletHeight = capabilityData.getUint16(3)
    this.config.pressureFactor = capabilityData.getUint16(5)
    this.config.width = capabilityData.getUint16(7)
    this.config.height = capabilityData.getUint16(9)
    this.config.refreshRate = capabilityData.getUint8(11)
    this.config.scaleFactor = this.config.tabletWidth / this.config.width

    const infoData = await this.readFeatureReport(this.commands.information)
    this.config.deviceName = this.extractStringFromDataView(infoData, 1, 7)
    this.config.firmware = `${infoData.getUint8(8)}.${infoData.getUint8(9)}.${infoData.getUint8(10)}.${infoData.getUint8(11)}`

    const serialData = await this.readFeatureReport(this.commands.eSerial)
    this.config.eSerial = this.extractStringFromDataView(serialData, 1)
  }

  handleInputReport(event) {
    if (!this.config.onPenDataCb) return

    if (event.reportId === this.commands.penData || event.reportId === this.commands.penDataTiming) {
      const packet = this.parsePenData(event)
      this.config.onPenDataCb(packet)
    }
  }

  parsePenData(event) {
    const packet = {
      isReady: (event.data.getUint8(0) & (1 << 0)) !== 0,
      isTouching: (event.data.getUint8(0) & (1 << 1)) !== 0,
      x: Math.trunc(event.data.getUint16(2) / this.config.scaleFactor),
      y: Math.trunc(event.data.getUint16(4) / this.config.scaleFactor),
      rawX: event.data.getUint16(2),
      rawY: event.data.getUint16(4),
      pressure: 0,
      sequence: null,
      timestamp: null,
    }

    event.data.setUint8(0, event.data.getUint8(0) & 0x0f)
    packet.pressure = event.data.getUint16(0) / this.config.pressureFactor

    if (event.reportId === this.commands.penDataTiming) {
      packet.timestamp = event.data.getUint16(6)
      packet.sequence = event.data.getUint16(8)
    }

    return packet
  }

  isConnected() {
    return this.device && this.device.opened === true
  }

  async sendFeatureReport(reportId, data) {
    if (!this.isConnected()) return
    await this.device.sendFeatureReport(reportId, data)
  }

  async readFeatureReport(reportId) {
    if (!this.isConnected()) return null
    return await this.device.receiveFeatureReport(reportId)
  }

  async setPenSettings(color, width) {
    if (!this.isConnected()) return
    const colorBytes = this.hexToRgbArray(color)
    const data = new Uint8Array([...colorBytes, Number.parseInt(width)])
    await this.sendFeatureReport(this.commands.penColorAndWidth, data)
  }

  async setBacklightBrightness(intensity) {
    if (!this.isConnected()) return
    const currentData = await this.readFeatureReport(this.commands.brightness)
    if (currentData && currentData.getUint8(1) === intensity) return
    await this.sendFeatureReport(this.commands.brightness, new Uint8Array([intensity, 0]))
  }

  async setBackgroundColor(color) {
    if (!this.isConnected()) return
    const colorBytes = this.hexToRgbArray(color)
    const currentData = await this.readFeatureReport(this.commands.backgroundColor)
    if (
        currentData &&
        currentData.getUint8(1) === colorBytes[0] &&
        currentData.getUint8(2) === colorBytes[1] &&
        currentData.getUint8(3) === colorBytes[2]
    ) {
      return
    }
    await this.sendFeatureReport(this.commands.backgroundColor, new Uint8Array(colorBytes))
  }

  async setActiveArea(area) {
    if (!this.isConnected()) return
    const packet = this.createDataPacket(8)
    packet.view.setUint16(0, area.x1, true)
    packet.view.setUint16(2, area.y1, true)
    packet.view.setUint16(4, area.x2, true)
    packet.view.setUint16(6, area.y2, true)
    await this.sendFeatureReport(this.commands.writingArea, packet.data)
  }

  async setWritingMode(mode) {
    if (!this.isConnected()) return
    await this.sendFeatureReport(this.commands.writingMode, new Uint8Array([mode]))
  }

  async setInkingEnabled(enabled) {
    if (!this.isConnected()) return
    await this.sendFeatureReport(this.commands.inkMode, new Uint8Array([enabled ? 1 : 0]))
  }

  async clearDisplay() {
    if (!this.isConnected()) return
    await this.sendFeatureReport(this.commands.clearScreen, new Uint8Array([0]))
  }

  async sendImageToDevice(imageData) {
    if (!this.isConnected()) return
    if (imageData) {
      this.imageCache = this.splitImageIntoChunks(imageData, this.config.chunkSize)
    }
    if (!this.imageCache) return

    await this.sendFeatureReport(this.commands.writeImageStart, new Uint8Array([this.config.imageFormat24BGR]))
    for (const chunk of this.imageCache) {
      await this.sendFeatureReport(this.commands.writeImageData, new Uint8Array([chunk.length, 0, ...chunk]))
    }
    await this.sendFeatureReport(this.commands.writeImageEnd, new Uint8Array([0]))
  }

  createDataPacket(size) {
    const data = new Uint8Array(size)
    const view = new DataView(data.buffer)
    return { data, view }
  }

  splitImageIntoChunks(array, chunkSize) {
    const chunks = []
    for (let i = 0; i < Math.ceil(array.length / chunkSize); i++) {
      const chunk = Array(chunkSize)
      for (let x = i * chunkSize, z = 0; x < (i + 1) * chunkSize; x++, z++) {
        chunk[z] = array[x]
      }
      chunks.push(chunk)
    }
    return chunks
  }

  extractStringFromDataView(dataView, offset, length) {
    const end = typeof length === "number" ? offset + length : dataView.byteLength
    let text = ""
    let val = -1
    while (offset < dataView.byteLength && offset < end) {
      val = dataView.getUint8(offset++)
      if (val === 0) break
      text += String.fromCharCode(val)
    }
    return text
  }

  hexToRgbArray(hex) {
    return hex
        .replace("#", "")
        .split(/(?<=^(?:.{2})+)(?!$)/)
        .map((component) => Number.parseInt("0x" + component, 16))
  }

  onPenData(callback) {
    this.config.onPenDataCb = callback
  }

  onDeviceChange(callback) {
    this.config.onHidChangeCb = callback
  }

  disconnect() {
    if (this.device) {
      this.device.close()
      this.device = null
    }
  }
}

export default {
  name: 'VTabletSign',

  props: {
    canvasWidth: {
      type: Number,
      default: 800
    },
    canvasHeight: {
      type: Number,
      default: 480
    },
    defaultImage: {
      type: String,
      default: '/placeholder.svg?height=480&width=800&text=Wacom+Tablet'
    },
    // New props for responsive sizing
    containerWidth: {
      type: Number,
      default: 400
    },
    containerHeight: {
      type: Number,
      default: 240
    },
    compact: {
      type: Boolean,
      default: false
    },
    required: {
      type: Boolean,
      default: false
    },
    clearTrigger: {
      type: Number,
      default: 0
    }
  },

  data() {
    return {
      isConnected: false,
      isConnecting: false,
      settings: {
        backgroundColor: '#FFFFFF',
        brightness: 3,
        inkEnabled: true,
        penColor: '#1566d1',
        penSize: 3,
        writingModeEnabled: true
      },
      // Internal state
      wacomDriver: null,
      canvasContext: null,
      currentImage: null,
      imageRequestPending: false,
      img: new Image(),
      currentStroke: null,
      isDrawing: false,
      lastPressure: 0,
      // Signature state
      signatureSaved: false,
      signatureDataURL: null,
      hasStrokes: false,
      connectionStatus: 'Connect'
    }
  },

  watch: {
    clearTrigger(newVal, oldVal) {
      if (newVal !== oldVal) {
        // this.resetSignature()
        try{

        setTimeout(() => {
          if (this.wacomDriver && this.isConnected) {
            this.wacomDriver.clearDisplay()
          }
        },200)
        }catch(e){
          console.error(e);
        }
      }
    }
  },

  async mounted() {
    // Initialize tablet driver first
    await this.initializeTablet()
    // Canvas context will be initialized when canvas becomes available
  },

  async beforeDestroy() {
    if (this.wacomDriver) {
      await this.wacomDriver.clearDisplay()
      this.wacomDriver.disconnect()
    }
  },

  computed: {
    scaleX() {
      return this.containerWidth / this.canvasWidth
    },

    scaleY() {
      return this.containerHeight / this.canvasHeight
    },

    isSignatureValid() {
      return !this.required || this.signatureSaved
    }
  },

  methods: {
    // Drawing utility functions
    calculateStrokeWidth(baseWidth) {
      const pressureFactor = this.lastPressure + 0.5
      return Math.max(baseWidth * pressureFactor, 0.5)
    },

    calculatePressureDifference(a, b) {
      return Math.abs(a - b)
    },

    createSvgPolyline(svgElement, color, width) {
      if (!svgElement) {
        console.warn('SVG element not available for creating polyline');
        return null;
      }
      const polyline = document.createElementNS("http://www.w3.org/2000/svg", "polyline")
      polyline.setAttributeNS(null, "style", `fill:none;stroke:${color};stroke-width:${width};`)
      svgElement.appendChild(polyline)
      return polyline
    },

    addPointToPolyline(polyline, svgElement, x, y) {
      if (!polyline || !svgElement) {
        console.warn('Polyline or SVG element not available for adding point');
        return;
      }
      const point = svgElement.createSVGPoint()
      point.x = x
      point.y = y
      polyline.points.appendItem(point)
    },

    processImageToRgb24(canvas, width, height) {
      const context = canvas.getContext("2d")
      const imageData = context.getImageData(0, 0, width, height)
      const rgb24 = new Uint8Array((imageData.data.length / 4) * 3)

      let sourceIndex = 0
      let targetIndex = 0

      while (sourceIndex < imageData.data.length) {
        rgb24[targetIndex++] = imageData.data[sourceIndex + 2] // Blue
        rgb24[targetIndex++] = imageData.data[sourceIndex + 1] // Green
        rgb24[targetIndex++] = imageData.data[sourceIndex + 0] // Red
        sourceIndex += 4 // Skip alpha channel
      }

      return rgb24
    },



    // Component Methods
    async initializeTablet() {
      this.wacomDriver = new WacomSTU540Driver()

      if (!this.wacomDriver) {
        console.error('WebHID not supported')
        return
      }

      const isAvailable = await this.wacomDriver.checkDeviceAvailability()
      if (!isAvailable) {
        this.isConnected = false
        this.connectionStatus = 'Connect'
      }

      this.wacomDriver.onDeviceChange((event) => {
        if (event === 'connect') {
          this.isConnected = true
          this.connectionStatus = 'Connected'
          this.$emit('connected')
        } else {
          this.isConnected = false
          this.connectionStatus = 'Connect'
          this.$emit('disconnected')
        }
      })

      this.wacomDriver.onPenData(this.handlePenData)
    },

    async handleConnect() {
      if (this.isConnecting) return

      this.isConnecting = true

      try {
        // Ensure wacomDriver is initialized
        if (!this.wacomDriver) {
          await this.initializeTablet();
        }

        if (!this.wacomDriver) {
          throw new Error('Failed to initialize Wacom driver');
        }

        const connected = await this.wacomDriver.connectToDevice()

        if (connected) {
          await this.setupInitialTabletSettings()
          this.isConnected = true
          this.connectionStatus = 'Connected'
          this.$emit('connected')
        }
      } catch (error) {
        console.error('Connection failed:', error)
      } finally {
        this.isConnecting = false
      }
    },

    async setupInitialTabletSettings() {
      // Initialize canvas context if not already done
      if (!this.canvasContext && this.$refs.canvasRef) {
        this.canvasContext = this.$refs.canvasRef.getContext('2d')
      }

      await this.wacomDriver.clearDisplay()
      await this.wacomDriver.setPenSettings(this.settings.penColor, this.settings.penSize)
      await this.wacomDriver.setWritingMode(1)
      await this.wacomDriver.setActiveArea({ x1: 0, y1: 0, x2: this.canvasWidth, y2: this.canvasHeight })
      await this.wacomDriver.setInkingEnabled(this.settings.inkEnabled)

      this.startNewStroke()

      this.img.onload = this.handleImageLoad
      setTimeout(() => {
        this.img.src = this.defaultImage
      }, 150)
    },

    handlePenData(penData) {
      this.$emit('penData', penData)
      this.processPenInput(
          penData.isTouching && this.settings.inkEnabled,
          penData.x,
          penData.y,
          penData.pressure
      )
    },

    async processPenInput(isTouching, x, y, pressure) {
      // Handle pen state changes
      if (isTouching !== this.isDrawing) {
        if (isTouching) {
          this.startNewStroke()

          if (x > 580 && y < 50 && !this.imageRequestPending) {
            await this.wacomDriver.setInkingEnabled(false)
            setTimeout(this.handleSendImage, 10)
            this.imageRequestPending = true
          }
        } else {
          this.finishCurrentStroke(x, y)
        }
        this.isDrawing = isTouching
      }

      // Add points while drawing and handle pressure changes
      if (isTouching && this.currentStroke) {
        // Create new stroke segment if pressure changed significantly
        if (this.calculatePressureDifference(pressure, this.lastPressure) > 0.02) {
          this.addPointToPolyline(this.currentStroke, this.$refs.svgRef, x, y)
          this.lastPressure = pressure
          this.startNewStroke()
        }

        // Always add the current point
        this.addPointToPolyline(this.currentStroke, this.$refs.svgRef, x, y)
      }

      // Update pressure even when not drawing for next stroke calculation
      if (pressure !== undefined) {
        this.lastPressure = pressure
      }
    },

    startNewStroke() {
      if (!this.$refs.svgRef) {
        console.warn('SVG ref not available for starting new stroke');
        return;
      }

      const strokeWidth = this.calculateStrokeWidth(parseInt(this.settings.penSize) + 1, this.lastPressure)
      this.currentStroke = this.createSvgPolyline(this.$refs.svgRef, this.settings.penColor, strokeWidth)

      if (this.currentStroke) {
        // Mark that we have strokes when a new stroke is started
        this.hasStrokes = true
      }
    },

    finishCurrentStroke(x, y) {
      if (this.currentStroke) {
        this.addPointToPolyline(this.currentStroke, this.$refs.svgRef, x, y)
      }
    },

    async handleImageLoad() {
      this.canvasContext.drawImage(this.img, 0, 0, this.canvasWidth, this.canvasHeight)

      if (!this.img.src.includes("placeholder")) {
        await this.wacomDriver.setActiveArea({
          x1: 0, y1: 0,
          x2: this.canvasWidth, y2: this.canvasHeight
        })
      } else {
        await this.wacomDriver.setActiveArea({
          x1: 16, y1: 50,
          x2: this.canvasWidth - 16, y2: this.canvasHeight - 16
        })

        this.canvasContext.font = "22px Arial"
        const currentDate = new Date().toLocaleString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })
        this.canvasContext.fillText(`Signed at ${currentDate}`, 130, 450)
      }

      this.currentImage = this.processImageToRgb24(this.$refs.canvasRef, this.canvasWidth, this.canvasHeight)
      await this.handleSendImage()

      this.$emit('imageLoaded', this.img.src)
    },

    handleFileUpload(event) {
      const file = event.target.files[0]
      if (!file) return

      if (file.type.match('image.*')) {
        const reader = new FileReader()
        reader.onload = (e) => {
          if (e.target.readyState === FileReader.DONE) {
            this.img.src = e.target.result
            this.img.onload = this.handleImageLoad
          }
        }
        reader.readAsDataURL(file)
      } else {
        alert('Please select a valid image file')
      }
    },

    async handleSaveSignature() {
      try {
        // Check if SVG ref exists
        if (!this.$refs.svgRef) {
          console.error('SVG reference not available');
          return;
        }


        // Check for actual polyline elements (the drawn strokes)
        const polylines = this.$refs.svgRef.querySelectorAll('polyline');

        if (polylines.length === 0) {
          console.warn('No polyline elements found - no actual drawing detected');
          alert('Please draw something on the tablet before saving');
          return;
        }

        const { dataURL, file } = await this.exportSvgAsTransparentPng(this.$refs.svgRef, 'signature.png')

        // Store signature state
        this.signatureSaved = true
        this.signatureDataURL = dataURL

        this.$emit('signatureSaved', { dataURL, file })

        /*setTimeout(() => {
          if (this.wacomDriver && this.isConnected) {
            this.wacomDriver.clearDisplay()
          }
        },500)*/
        await this.handleClearScreen();

        // Properly disconnect device after saving
        this.resetSignature();
      } catch (error) {
        console.error('Error saving signature:', error)
      }
    },

    exportSvgAsTransparentPng(svgElement, filename = "signature-transparent.png") {
      return new Promise((resolve, reject) => {
        try {
          // Create SVG data URL with transparent background
          svgElement.setAttribute("xmlns", "http://www.w3.org/2000/svg")
          const svgContent = svgElement.outerHTML

          const svgDataUrl = `data:image/svg+xml;base64,${btoa(encodeURIComponent(svgContent).replace(/%([0-9A-F]{2})/g, (_, p1) => String.fromCharCode(parseInt(p1, 16))))}`

          // Create image and draw to transparent canvas
          const img = new Image()
          img.onload = async () => {
            try {
              // Create 300x170 resolution canvas for both download and form
              const canvas = document.createElement('canvas')
              const ctx = canvas.getContext('2d')
              canvas.width = 300
              canvas.height = 170
              ctx.drawImage(img, 0, 0, 300, 170)
              const dataURL = canvas.toDataURL('image/png')

              // Convert to File object for form input
              const response = await fetch(dataURL)
              const blob = await response.blob()
              const file = new File([blob], filename, { type: 'image/png' })

              resolve({
                dataURL, // 300x170 resolution
                file // 300x170 resolution
              })
            } catch (error) {
              reject(error)
            }
          }
          img.onerror = () => reject(new Error('Failed to load SVG image'))
          img.src = svgDataUrl
        } catch (error) {
          reject(error)
        }
      })
    },

    async handleClearScreen() {

      // Check if refs exist before accessing them
      if (!this.$refs.svgRef) {
        return;
      }


      if (this.wacomDriver && this.isConnected) {
        await this.wacomDriver.clearDisplay()
      }

      if (this.canvasContext) {
        this.canvasContext.fillStyle = this.settings.backgroundColor
        this.canvasContext.fillRect(0, 0, this.canvasWidth, this.canvasHeight)
      }

      if (this.$refs.svgRef) {
        this.$refs.svgRef.innerHTML = ''
      }

      // Reset signature state but don't clear if signature was saved (for display purposes)
      if (!this.signatureSaved) {
        this.signatureDataURL = null
      }

      // Reset stroke tracking
      this.hasStrokes = false

      this.$emit('signatureCleared')
    },

    resetSignature() {

      // Reset all signature state
      this.signatureSaved = false
      this.signatureDataURL = null
      this.isConnected = false
      this.isConnecting = false
      this.connectionStatus = 'Connect'
      this.hasStrokes = false

      // Disconnect from device if connected but keep driver instance
      if (this.wacomDriver && this.wacomDriver.device) {
        try {
          this.wacomDriver.device.close();
          this.wacomDriver.device = null;
        } catch (error) {
          console.log('Error closing device:', error);
        }
      }

      // Clear the canvas and SVG
      if (this.canvasContext) {
        this.canvasContext.fillStyle = this.settings.backgroundColor
        this.canvasContext.fillRect(0, 0, this.canvasWidth, this.canvasHeight)
      }
      if (this.$refs.svgRef) {
        this.$refs.svgRef.innerHTML = ''
      }

    },

    async handleSendImage() {
      await this.wacomDriver.sendImageToDevice(this.currentImage)
      this.imageRequestPending = false
      if (this.$refs.svgRef) {
        this.$refs.svgRef.innerHTML = ''
      }
      await this.handleInkModeChange()
    },

    handleSendDefaultImage() {
      this.img.src = this.defaultImage
      this.handleSendImage()
    },

    async handleBackgroundChange() {
      await this.wacomDriver.setBackgroundColor(this.settings.backgroundColor)
      await this.handleClearScreen()
    },

    async handleBrightnessChange() {
      await this.wacomDriver.setBacklightBrightness(this.settings.brightness)
    },

    async handleInkModeChange() {
      await this.wacomDriver.setInkingEnabled(this.settings.inkEnabled)
    },

    async handlePenSettingsChange() {
      await this.wacomDriver.setPenSettings(this.settings.penColor, this.settings.penSize)
    },

    async handleWritingModeChange() {
      await this.wacomDriver.setWritingMode(this.settings.writingModeEnabled ? 1 : 0)
    }
  }
}
</script>

<style scoped>
.v-tablet-sign {
  max-width: 100%;
  padding: 20px;
  margin: 20px auto;
  text-align: center;
}

.v-tablet-sign--disconnected {
  opacity: 0.6;
}

.v-tablet-sign__header {
  margin-bottom: 20px;
}

/* Compact mode for form integration */
.v-tablet-sign--compact {
  padding: 10px;
  margin: 10px 0;
}

.v-tablet-sign--compact .v-tablet-sign__header {
  margin-bottom: 10px;
}

.v-tablet-sign--compact .v-tablet-sign__file-controls {
  gap: 4px;
}

.v-tablet-sign__canvas-container {
  position: relative;
  margin: 0 auto 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.v-tablet-sign__canvas-container--compact {
  margin: 0 auto 10px;
  border: 1px solid #e0e0e0;
}

.v-tablet-sign__canvas {
  position: absolute;
  top: 0;
  left: 0;
  background: #FFFFFF;
  transform-origin: top left;
}

.v-tablet-sign__svg {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  transform-origin: top left;
}

.v-tablet-sign__preview {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.v-tablet-sign__file-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
  margin-top: 16px;
  padding: 16px;
}

.v-tablet-sign__disconnected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
  background-color: #fafafa;
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
}

.v-tablet-sign__header {
  display: flex;
  justify-content: center;
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #fafafa;
}

</style>